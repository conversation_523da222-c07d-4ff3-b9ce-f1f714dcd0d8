using UnityEngine;

namespace Storage
{
    /// <summary>
    /// Storage模块主线程处理器
    /// 负责处理从后台线程发送到主线程的回调操作
    /// </summary>
    public class StorageMainThreadProcessor : MonoBehaviour
    {
        private static StorageMainThreadProcessor _instance;

        /// <summary>
        /// 获取或创建单例实例
        /// </summary>
        public static StorageMainThreadProcessor Instance
        {
            get
            {
                if (_instance == null)
                {
                    var go = new GameObject("StorageMainThreadProcessor");
                    _instance = go.AddComponent<StorageMainThreadProcessor>();
                    DontDestroyOnLoad(go);
                }
                return _instance;
            }
        }

        /// <summary>
        /// 确保处理器已创建
        /// </summary>
        public static void EnsureCreated()
        {
            _ = Instance;
        }

        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }

        private void Update()
        {
            // 处理主线程队列中的操作
            Storage.ExecuteMainThreadActions();
        }

        private void OnDestroy()
        {
            if (_instance == this)
            {
                _instance = null;
            }
        }
    }
}
