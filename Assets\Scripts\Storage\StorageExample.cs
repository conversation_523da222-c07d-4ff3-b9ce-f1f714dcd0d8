using System;
using System.Collections.Generic;
using UnityEngine;
using Storage;

/// <summary>
/// Storage系统使用示例
/// 演示如何使用Storage模块进行数据持久化
/// </summary>
public class StorageExample : MonoBehaviour
{
    [Header("测试数据")]
    [SerializeField] private int _playerLevel = 1;
    [SerializeField] private float _playerExperience = 0f;
    [SerializeField] private string _playerName = "Player";
    [SerializeField] private bool _soundEnabled = true;
    [SerializeField] private Vector3 _playerPosition = Vector3.zero;
    [SerializeField] private Color _playerColor = Color.white;

    [Header("设置")]
    [SerializeField] private bool _useEncryption = false;
    [SerializeField] private StorageSettings.EncryptionType _encryptionType = StorageSettings.EncryptionType.AES;
    [SerializeField] private string _encryptionPassword = "MySecretPassword";
    [SerializeField] private string _saveFileName = "GameData.json";

    private StorageSettings _customSettings;

    #region Unity生命周期

    private void Start()
    {
        // 初始化Storage系统
        InitializeStorage();

        // 加载保存的数据
        LoadGameData();

        // 显示当前数据
        LogCurrentData();
    }

    private void Update()
    {
        // 按键测试
        HandleInput();
    }

    #endregion

    #region 初始化

    /// <summary>
    /// 初始化Storage系统
    /// </summary>
    private void InitializeStorage()
    {
        // 创建自定义设置
        _customSettings = new StorageSettings(_saveFileName);

        if (_useEncryption)
        {
            _customSettings.Encryption = _encryptionType;
            _customSettings.EncryptionPassword = _encryptionPassword;
        }

        // 初始化Storage系统
        Storage.Storage.Initialize(_customSettings);

        Debug.Log("[StorageExample] InitializeStorage - Storage system initialized");
    }

    #endregion

    #region 数据操作

    /// <summary>
    /// 保存游戏数据
    /// </summary>
    public void SaveGameData()
    {
        Debug.Log("[StorageExample] SaveGameData - Saving game data...");

        // 保存基础类型数据
        Storage.Storage.Set("PlayerLevel", _playerLevel);
        Storage.Storage.Set("PlayerExperience", _playerExperience);
        Storage.Storage.Set("PlayerName", _playerName);
        Storage.Storage.Set("SoundEnabled", _soundEnabled);

        // 保存Unity类型数据
        Storage.Storage.Set("PlayerPosition", _playerPosition);
        Storage.Storage.Set("PlayerColor", _playerColor);

        // 保存集合数据
        var inventory = new List<string> { "Sword", "Shield", "Potion" };
        Storage.Storage.Set("Inventory", inventory);

        var stats = new Dictionary<string, int>
        {
            { "Strength", 10 },
            { "Agility", 8 },
            { "Intelligence", 12 }
        };
        Storage.Storage.Set("PlayerStats", stats);

        // 保存自定义类型数据
        var playerData = new PlayerData
        {
            Name = _playerName,
            Level = _playerLevel,
            Experience = _playerExperience,
            Position = _playerPosition
        };
        Storage.Storage.Set("PlayerData", playerData);

        // 保存到文件
        Storage.Storage.SaveToFileSync(_customSettings);

        Debug.Log("[StorageExample] SaveGameData - Game data saved successfully");
    }

    /// <summary>
    /// 加载游戏数据
    /// </summary>
    public void LoadGameData()
    {
        Debug.Log("[StorageExample] LoadGameData - Loading game data...");

        // 从文件加载数据
        Storage.Storage.LoadFromFileSync(_customSettings);

        // 使用TryGet方法加载基础类型数据，显示是否存在
        if (Storage.Storage.TryGet("PlayerLevel", out int level))
        {
            Debug.Log($"[StorageExample] LoadGameData - 找到存储的玩家等级: {level}");
            _playerLevel = level;
        }
        else
        {
            Debug.Log($"[StorageExample] LoadGameData - 未找到玩家等级，使用默认值: {_playerLevel}");
        }

        // 使用TryGet带默认值的方法，更简洁地获取并设置值
        if (Storage.Storage.TryGet("PlayerExperience", out float exp, _playerExperience))
        {
            Debug.Log($"[StorageExample] LoadGameData - 找到存储的玩家经验值: {exp}");
        }
        else
        {
            Debug.Log($"[StorageExample] LoadGameData - 未找到玩家经验值，使用默认值: {exp}");
        }
        _playerExperience = exp;

        // 对于设置默认值的情况，可以使用TryGet方法
        Storage.Storage.TryGet("PlayerName", out string name, _playerName);
        _playerName = name;

        Storage.Storage.TryGet("SoundEnabled", out bool soundEnabled, _soundEnabled);
        _soundEnabled = soundEnabled;

        Storage.Storage.TryGet("PlayerPosition", out Vector3 position, _playerPosition);
        _playerPosition = position;

        Storage.Storage.TryGet("PlayerColor", out Color color, _playerColor);
        _playerColor = color;

        // 加载集合数据，使用TryGet方法可以清晰地知道是否找到了数据
        if (Storage.Storage.TryGet("Inventory", out List<string> inventory))
        {
            Debug.Log($"[StorageExample] LoadGameData - Inventory: {string.Join(", ", inventory)}");
        }
        else
        {
            Debug.Log("[StorageExample] LoadGameData - 未找到物品栏数据");
        }

        // 对于复杂对象，TryGet能清晰区分“不存在”和“存在但值为null”的情况
        if (Storage.Storage.TryGet("PlayerStats", out Dictionary<string, int> stats))
        {
            Debug.Log("[StorageExample] LoadGameData - Player Stats:");
            foreach (var stat in stats)
            {
                Debug.Log($"  {stat.Key}: {stat.Value}");
            }
        }
        else
        {
            Debug.Log("[StorageExample] LoadGameData - 未找到玩家属性数据");
        }

        // 加载自定义类型数据
        if (Storage.Storage.TryGet("PlayerData", out PlayerData playerData))
        {
            Debug.Log($"[StorageExample] LoadGameData - Player Data: {playerData}");
        }
        else
        {
            Debug.Log("[StorageExample] LoadGameData - 未找到玩家数据对象");
        }

        Debug.Log("[StorageExample] LoadGameData - Game data loaded successfully");
    }

    /// <summary>
    /// 清除所有数据
    /// </summary>
    public void ClearAllData()
    {
        Storage.Storage.Clear();
        Storage.Storage.DeleteFile(_customSettings);
        Debug.Log("[StorageExample] ClearAllData - All data cleared");
    }

    #endregion

    #region 输入处理

    /// <summary>
    /// 处理键盘输入
    /// </summary>
    private void HandleInput()
    {
        if (Input.GetKeyDown(KeyCode.S))
        {
            SaveGameData();
        }
        else if (Input.GetKeyDown(KeyCode.L))
        {
            LoadGameData();
        }
        else if (Input.GetKeyDown(KeyCode.C))
        {
            ClearAllData();
        }
        else if (Input.GetKeyDown(KeyCode.D))
        {
            LogCurrentData();
        }
        else if (Input.GetKeyDown(KeyCode.U))
        {
            UpdateTestData();
        }
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 更新测试数据
    /// </summary>
    private void UpdateTestData()
    {
        _playerLevel++;
        _playerExperience += 100f;
        _playerPosition += Vector3.one;
        _playerColor = new Color(UnityEngine.Random.value, UnityEngine.Random.value, UnityEngine.Random.value, 1f);

        Debug.Log("[StorageExample] UpdateTestData - Test data updated");
        LogCurrentData();
    }

    /// <summary>
    /// 输出当前数据
    /// </summary>
    private void LogCurrentData()
    {
        Debug.Log("[StorageExample] Current Data:");
        Debug.Log($"  Player Level: {_playerLevel}");
        Debug.Log($"  Player Experience: {_playerExperience}");
        Debug.Log($"  Player Name: {_playerName}");
        Debug.Log($"  Sound Enabled: {_soundEnabled}");
        Debug.Log($"  Player Position: {_playerPosition}");
        Debug.Log($"  Player Color: {_playerColor}");
    }

    #endregion

    #region GUI

    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("Storage System Example", GUI.skin.box);
        GUILayout.Space(10);

        if (GUILayout.Button("Save Data (S)"))
        {
            SaveGameData();
        }

        if (GUILayout.Button("Load Data (L)"))
        {
            LoadGameData();
        }

        if (GUILayout.Button("Update Test Data (U)"))
        {
            UpdateTestData();
        }

        if (GUILayout.Button("Clear All Data (C)"))
        {
            ClearAllData();
        }

        if (GUILayout.Button("Log Current Data (D)"))
        {
            LogCurrentData();
        }

        GUILayout.Space(10);
        GUILayout.Label($"Player Level: {_playerLevel}");
        GUILayout.Label($"Player Experience: {_playerExperience:F1}");
        GUILayout.Label($"Encryption: {(_useEncryption ? _encryptionType.ToString() : "None")}");

        GUILayout.EndArea();
    }

    #endregion
}

/// <summary>
/// 自定义玩家数据类，用于测试复杂类型序列化
/// </summary>
[Serializable]
public class PlayerData
{
    public string Name;
    public int Level;
    public float Experience;
    public Vector3 Position;

    public override string ToString()
    {
        return $"PlayerData(Name: {Name}, Level: {Level}, Experience: {Experience}, Position: {Position})";
    }
}
