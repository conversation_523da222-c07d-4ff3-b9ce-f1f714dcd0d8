2025-05-26 02:36:18.1532 [INFO] [Storage]::Initialize(117) - Storage system initialized successfully
2025-05-26 02:36:18.1749 [INFO] [StorageCache]::Set(87) - Key: test_string, Type: String
2025-05-26 02:36:18.1749 [INFO] [Storage]::Set(150) - Key: test_string, Type: String
2025-05-26 02:36:18.1749 [INFO] [StorageCache]::Set(87) - Key: test_int, Type: Int32
2025-05-26 02:36:18.1749 [INFO] [Storage]::Set(150) - Key: test_int, Type: Int32
2025-05-26 02:36:18.1749 [INFO] [StorageCache]::Set(87) - Key: test_float, Type: Single
2025-05-26 02:36:18.1749 [INFO] [Storage]::Set(150) - Key: test_float, Type: Single
2025-05-26 02:36:18.1749 [INFO] [StorageCache]::Set(87) - Key: test_bool, Type: Boolean
2025-05-26 02:36:18.1749 [INFO] [Storage]::Set(150) - Key: test_bool, Type: Boolean
2025-05-26 02:36:18.7076 [ERROR] [Storage]::SaveToFile(292) - Failed to save data: get_persistentDataPath can only be called from the main thread.
Constructors and field initializers will be executed from the loading thread when loading a scene.
Don't use this function in the constructor or field initializers, instead move initialization code to the Awake or Start function.
2025-05-26 02:36:21.6599 [ERROR] [Storage]::SaveToFile(292) - Failed to save data: get_persistentDataPath can only be called from the main thread.
Constructors and field initializers will be executed from the loading thread when loading a scene.
Don't use this function in the constructor or field initializers, instead move initialization code to the Awake or Start function.
2025-05-26 02:36:21.6599 [ERROR] [Storage]::OnApplicationFocusChanged(810) - Failed to auto-save data on application pause: get_persistentDataPath can only be called from the main thread.
Constructors and field initializers will be executed from the loading thread when loading a scene.
Don't use this function in the constructor or field initializers, instead move initialization code to the Awake or Start function.
2025-05-26 02:36:48.7255 [ERROR] [Storage]::SaveToFile(292) - Failed to save data: get_persistentDataPath can only be called from the main thread.
Constructors and field initializers will be executed from the loading thread when loading a scene.
Don't use this function in the constructor or field initializers, instead move initialization code to the Awake or Start function.
2025-05-26 02:36:48.7255 [ERROR] [Storage]::OnApplicationFocusChanged(810) - Failed to auto-save data on application pause: get_persistentDataPath can only be called from the main thread.
Constructors and field initializers will be executed from the loading thread when loading a scene.
Don't use this function in the constructor or field initializers, instead move initialization code to the Awake or Start function.
