using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using UnityEngine;
using Storage.Serialization;
using Storage.Encryption;
using DGame.Framework;

namespace Storage
{
    /// <summary>
    /// 文件操作完成回调委托
    /// </summary>
    /// <param name="success">操作是否成功</param>
    /// <param name="errorMessage">错误信息（如果有）</param>
    public delegate void FileOperationCallback(bool success, string errorMessage = null);

    /// <summary>
    /// 文件读取完成回调委托
    /// </summary>
    /// <param name="success">操作是否成功</param>
    /// <param name="data">读取的数据（如果成功）</param>
    /// <param name="errorMessage">错误信息（如果有）</param>
    public delegate void FileReadCallback(bool success, Dictionary<string, object> data = null, string errorMessage = null);

    /// <summary>
    /// 高性能数据持久化模块的主要API类
    /// 提供基于key-value的数据增删改查操作，支持内存缓存和文件持久化
    /// </summary>
    public static class Storage
    {
        #region 私有字段

        private static StorageCache _cache;
        private static StorageSettings _defaultSettings;
        private static bool _isInitialized = false;
        private static readonly Queue<Action> _mainThreadActions = new Queue<Action>();
        private static readonly object _mainThreadLock = new object();
        private static int _mainThreadId;

        #endregion

        #region 属性

        /// <summary>
        /// 默认设置
        /// </summary>
        public static StorageSettings DefaultSettings
        {
            get
            {
                if (_defaultSettings == null)
                {
                    _defaultSettings = new StorageSettings();
                }
                return _defaultSettings;
            }
            set
            {
                _defaultSettings = value;
            }
        }

        /// <summary>
        /// 内存缓存
        /// </summary>
        private static StorageCache Cache
        {
            get
            {
                if (_cache == null)
                {
                    _cache = new StorageCache();
                }
                return _cache;
            }
        }

        /// <summary>
        /// 是否已初始化
        /// </summary>
        public static bool IsInitialized
        {
            get { return _isInitialized; }
        }

        /// <summary>
        /// 主线程ID，用于线程安全检查
        /// </summary>
        public static int MainThreadId
        {
            get { return _mainThreadId; }
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化存储系统
        /// </summary>
        /// <param name="settings">设置对象</param>
        public static void Initialize(StorageSettings settings = null)
        {
            if (_isInitialized)
            {
                NLogger.LogWarning("Storage system is already initialized");
                return;
            }

            // 记录主线程ID
            _mainThreadId = Thread.CurrentThread.ManagedThreadId;

            _defaultSettings = settings ?? new StorageSettings();
            _cache = new StorageCache();

            // 初始化路径缓存（保证在主线程调用）
            StorageSettings.InitializePathCache();

            // 确保主线程处理器已创建
            StorageMainThreadProcessor.EnsureCreated();

            // 注册应用程序事件
            RegisterApplicationEvents();

            _isInitialized = true;
            NLogger.Log("Storage system initialized successfully");
        }

        /// <summary>
        /// 注册应用程序事件
        /// </summary>
        private static void RegisterApplicationEvents()
        {
            Application.quitting += OnApplicationQuitting;
            Application.focusChanged += OnApplicationFocusChanged;
        }

        #endregion

        #region 数据操作方法

        /// <summary>
        /// 保存数据到内存缓存
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <param name="value">值</param>
        public static void Set<T>(string key, T value)
        {
            EnsureInitialized();

            if (string.IsNullOrEmpty(key))
            {
                NLogger.LogError("Key cannot be null or empty");
                return;
            }

            Cache.Set(key, value);
            NLogger.Log("Key: {0}, Type: {1}, Value: {2}", arg0: key, arg1: typeof(T).Name, arg2: value);
        }

        /// <summary>
        /// 尝试从内存缓存获取数据
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <param name="value">输出的数据值</param>
        /// <returns>是否成功获取到值，成功返回true，失败返回false</returns>
        public static bool TryGet<T>(string key, out T value)
        {
            value = default;
            EnsureInitialized();

            if (string.IsNullOrEmpty(key))
            {
                NLogger.LogError("Key cannot be null or empty");
                return false;
            }

            return Cache.TryGet(key, out value);
        }



        /// <summary>
        /// 尝试从内存缓存获取数据，如果不存在则设置为默认值
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <param name="value">输出的数据值</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>是否成功获取到值，成功返回true，失败返回false</returns>
        public static bool TryGet<T>(string key, out T value, T defaultValue)
        {
            bool success = TryGet<T>(key, out value);
            if (!success)
            {
                value = defaultValue;
            }
            return success;
        }



        /// <summary>
        /// 检查键是否存在
        /// </summary>
        /// <param name="key">键</param>
        /// <returns>是否存在</returns>
        public static bool ContainsKey(string key)
        {
            EnsureInitialized();

            if (string.IsNullOrEmpty(key))
                return false;

            return Cache.ContainsKey(key);
        }

        /// <summary>
        /// 删除数据
        /// </summary>
        /// <param name="key">键</param>
        /// <returns>是否删除成功</returns>
        public static bool Remove(string key)
        {
            EnsureInitialized();

            if (string.IsNullOrEmpty(key))
            {
                NLogger.LogError("Key cannot be null or empty");
                return false;
            }

            return Cache.Remove(key);
        }

        /// <summary>
        /// 清空所有数据
        /// </summary>
        public static void Clear()
        {
            EnsureInitialized();
            Cache.Clear();
            NLogger.Log("All data cleared from cache");
        }

        /// <summary>
        /// 获取所有键
        /// </summary>
        /// <returns>键的集合</returns>
        public static ICollection<string> GetAllKeys()
        {
            EnsureInitialized();
            return Cache.Keys;
        }

        #endregion

        #region 文件操作方法

        /// <summary>
        /// 异步保存所有数据到文件
        /// </summary>
        /// <param name="settings">设置对象</param>
        /// <param name="callback">完成回调</param>
        public static void SaveToFileAsync(StorageSettings settings = null, FileOperationCallback callback = null)
        {
            EnsureInitialized();

            // 性能优化：检查缓存是否有脏数据，如果没有，直接返回成功
            if (!Cache.IsDirty)
            {
                NLogger.Log("Cache is not dirty, skipping file save operation");
                callback?.Invoke(true);
                return;
            }

            var actualSettings = settings ?? DefaultSettings;
            var data = Cache.GetAllData();

            // 根据存储位置使用不同的存储策略
            if (actualSettings.Location == StorageSettings.StorageLocation.PlayerPrefs)
            {
                // PlayerPrefs必须在主线程操作
                SaveToPlayerPrefs(data, actualSettings, callback);
            }
            else if (actualSettings.Location == StorageSettings.StorageLocation.File)
            {
                // 文件操作在后台线程执行
                SaveToFileInBackground(data, actualSettings, callback);
            }
            else
            {
                callback?.Invoke(false, $"Storage location {actualSettings.Location} is not supported for writing");
            }
        }

        /// <summary>
        /// 在主线程中保存数据到PlayerPrefs
        /// </summary>
        /// <param name="data">要保存的数据</param>
        /// <param name="settings">存储设置</param>
        /// <param name="callback">回调函数</param>
        private static void SaveToPlayerPrefs(Dictionary<string, object> data, StorageSettings settings, FileOperationCallback callback)
        {
            if (Thread.CurrentThread.ManagedThreadId != _mainThreadId)
            {
                NLogger.LogError("Cannot access PlayerPrefs from a background thread");
                callback?.Invoke(false, "Cannot access PlayerPrefs from a background thread");
                return;
            }

            try
            {
                // 使用核心方法处理数据序列化和加密
                string json = SerializeAndEncrypt(data, settings);

                // 使用核心方法保存数据（在主线程中运行，不需要额外的线程安全检查）
                bool success = SaveDataByLocation(json, settings);

                if (success)
                {
                    // 执行回调
                    Cache.MarkAsSaved();
                    NLogger.Log("Saved {0} items to PlayerPrefs: {1}", arg0: data.Count, arg1: settings.FilePath);
                    callback?.Invoke(true);
                }
                else
                {
                    callback?.Invoke(false, "Failed to save data to PlayerPrefs");
                }
            }
            catch (Exception ex)
            {
                NLogger.LogError("Failed to save data to PlayerPrefs: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                callback?.Invoke(false, ex.Message);
            }
        }

        /// <summary>
        /// 在后台线程中保存数据到文件
        /// </summary>
        /// <param name="data">要保存的数据</param>
        /// <param name="settings">存储设置</param>
        /// <param name="callback">回调函数</param>
        private static void SaveToFileInBackground(Dictionary<string, object> data, StorageSettings settings, FileOperationCallback callback)
        {
            var thread = new Thread(() =>
            {
                try
                {
                    // 使用核心方法处理数据序列化和加密
                    string json = SerializeAndEncrypt(data, settings);

                    // 使用核心方法根据存储位置保存数据
                    bool success = SaveDataByLocation(json, settings);

                    // 在主线程执行回调
                    RunOnMainThread(() =>
                    {
                        if (success)
                        {
                            Cache.MarkAsSaved();
                            NLogger.Log("Saved {0} items to file: {1}", arg0: data.Count, arg1: settings.GetFullPath());
                            callback?.Invoke(true);
                        }
                        else
                        {
                            callback?.Invoke(false, "Failed to save data due to storage location error");
                        }
                    });
                }
                catch (Exception ex)
                {
                    // 在主线程执行错误回调
                    RunOnMainThread(() =>
                    {
                        NLogger.LogError("Failed to save data to file: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                        callback?.Invoke(false, ex.Message);
                    });
                }
            })
            {
                Name = "Storage-SaveToFile",
                IsBackground = true
            };

            thread.Start();
        }

        /// <summary>
        /// 异步从文件加载数据
        /// </summary>
        /// <param name="settings">设置对象</param>
        /// <param name="callback">完成回调</param>
        public static void LoadFromFileAsync(StorageSettings settings = null, FileReadCallback callback = null)
        {
            EnsureInitialized();

            var actualSettings = settings ?? DefaultSettings;

            // 根据存储位置使用不同的读取策略
            if (actualSettings.Location == StorageSettings.StorageLocation.PlayerPrefs)
            {
                // PlayerPrefs必须在主线程操作
                LoadFromPlayerPrefs(actualSettings, callback);
            }
            else if (actualSettings.Location == StorageSettings.StorageLocation.File)
            {
                // 文件操作在后台线程执行
                LoadFromFileInBackground(actualSettings, callback);
            }
            else
            {
                callback?.Invoke(false, null, $"Storage location {actualSettings.Location} is not supported for reading");
            }
        }

        /// <summary>
        /// 从PlayerPrefs加载数据（主线程执行）
        /// </summary>
        /// <param name="settings">存储设置</param>
        /// <param name="callback">回调函数</param>
        private static void LoadFromPlayerPrefs(StorageSettings settings, FileReadCallback callback)
        {
            if (Thread.CurrentThread.ManagedThreadId != _mainThreadId)
            {
                NLogger.LogError("Cannot access PlayerPrefs from a background thread");
                callback?.Invoke(false, null, "Cannot access PlayerPrefs from a background thread");
                return;
            }

            try
            {
                // 使用核心方法根据存储位置加载数据
                string json = LoadDataByLocation(settings);

                if (string.IsNullOrEmpty(json))
                {
                    NLogger.LogWarning("PlayerPrefs key is empty or does not exist: {0}", arg0: settings.FilePath);
                    callback?.Invoke(false, null, "PlayerPrefs key is empty or does not exist");
                    return;
                }

                // 使用核心方法处理数据解密和反序列化
                var data = DecryptAndDeserialize(json, settings);

                // 直接执行回调
                Cache.LoadFromDictionary(data);
                NLogger.Log("Loaded {0} items from PlayerPrefs: {1}", arg0: data.Count, arg1: settings.FilePath);
                callback?.Invoke(true, data);
            }
            catch (Exception ex)
            {
                NLogger.LogError("Failed to load data from PlayerPrefs: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                callback?.Invoke(false, null, ex.Message);
            }
        }

        /// <summary>
        /// 在后台线程中从文件加载数据
        /// </summary>
        /// <param name="settings">存储设置</param>
        /// <param name="callback">回调函数</param>
        private static void LoadFromFileInBackground(StorageSettings settings, FileReadCallback callback)
        {
            var thread = new Thread(() =>
            {
                try
                {
                    // 使用核心方法根据存储位置加载数据
                    string json = LoadDataByLocation(settings);

                    if (string.IsNullOrEmpty(json))
                    {
                        RunOnMainThread(() =>
                        {
                            NLogger.LogWarning("File is empty or does not exist: {0}", arg0: settings.GetFullPath());
                            callback?.Invoke(false, null, "File is empty or does not exist");
                        });
                        return;
                    }

                    // 使用核心方法处理数据解密和反序列化
                    var data = DecryptAndDeserialize(json, settings);

                    // 在主线程执行回调
                    RunOnMainThread(() =>
                    {
                        Cache.LoadFromDictionary(data);
                        NLogger.Log("Loaded {0} items from file: {1}", arg0: data.Count, arg1: settings.GetFullPath());
                        callback?.Invoke(true, data);
                    });
                }
                catch (Exception ex)
                {
                    // 在主线程执行错误回调
                    RunOnMainThread(() =>
                    {
                        NLogger.LogError("Failed to load data from file: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                        callback?.Invoke(false, null, ex.Message);
                    });
                }
            })
            {
                Name = "Storage-LoadFromFile",
                IsBackground = true
            };

            thread.Start();
        }

        /// <summary>
        /// 检查文件是否存在
        /// </summary>
        /// <param name="settings">设置对象</param>
        /// <returns>文件是否存在</returns>
        public static bool FileExists(StorageSettings settings = null)
        {
            var actualSettings = settings ?? DefaultSettings;

            switch (actualSettings.Location)
            {
                case StorageSettings.StorageLocation.File:
                    return File.Exists(actualSettings.GetFullPath());
                case StorageSettings.StorageLocation.PlayerPrefs:
                    return PlayerPrefs.HasKey(actualSettings.FilePath);
                default:
                    return false;
            }
        }

        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="settings">设置对象</param>
        public static void DeleteFile(StorageSettings settings = null)
        {
            var actualSettings = settings ?? DefaultSettings;

            try
            {
                switch (actualSettings.Location)
                {
                    case StorageSettings.StorageLocation.File:
                        if (File.Exists(actualSettings.GetFullPath()))
                        {
                            File.Delete(actualSettings.GetFullPath());
                            NLogger.Log("File deleted: {0}", arg0: actualSettings.GetFullPath());
                        }
                        // 同时删除可能存在的备份文件
                        if (File.Exists(actualSettings.GetBackupPath()))
                        {
                            File.Delete(actualSettings.GetBackupPath());
                            NLogger.Log("Backup file deleted: {0}", arg0: actualSettings.GetBackupPath());
                        }
                        break;
                    case StorageSettings.StorageLocation.PlayerPrefs:
                        PlayerPrefs.DeleteKey(actualSettings.FilePath);
                        NLogger.Log("PlayerPrefs key deleted: {0}", arg0: actualSettings.FilePath);
                        break;
                }
            }
            catch (Exception ex)
            {
                NLogger.LogError("Failed to delete file: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 确保系统已初始化
        /// </summary>
        private static void EnsureInitialized()
        {
            if (!_isInitialized)
            {
                throw new InvalidOperationException("Storage system has not been initialized. Call Initialize() first.");
            }
        }

        /// <summary>
        /// 在主线程执行操作
        /// </summary>
        /// <param name="action">要执行的操作</param>
        private static void RunOnMainThread(Action action)
        {
            if (action == null)
                return;

            if (Thread.CurrentThread.ManagedThreadId == _mainThreadId)
            {
                action();
            }
            else
            {
                lock (_mainThreadLock)
                {
                    _mainThreadActions.Enqueue(action);
                }
            }
        }

        /// <summary>
        /// 执行主线程操作队列中的操作
        /// 由StorageMainThreadProcessor.Update调用
        /// </summary>
        internal static void ExecuteMainThreadActions()
        {
            lock (_mainThreadLock)
            {
                while (_mainThreadActions.Count > 0)
                {
                    Action action = _mainThreadActions.Dequeue();
                    try
                    {
                        action();
                    }
                    catch (Exception ex)
                    {
                        NLogger.LogError("Error executing main thread action: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                    }
                }
            }
        }

        #region 数据处理核心方法

        /// <summary>
        /// 处理数据序列化和加密
        /// </summary>
        /// <param name="data">要处理的数据</param>
        /// <param name="settings">存储设置</param>
        /// <returns>处理后的JSON字符串</returns>
        private static string SerializeAndEncrypt(Dictionary<string, object> data, StorageSettings settings)
        {
            // 序列化
            string json = StorageSerializer.SerializeToJson(data, settings.PrettyPrint);

            // 加密处理
            if (settings.Encryption != StorageSettings.EncryptionType.None)
            {
                json = EncryptData(json, settings);
            }

            return json;
        }

        /// <summary>
        /// 处理数据解密和反序列化
        /// </summary>
        /// <param name="json">要处理的JSON字符串</param>
        /// <param name="settings">存储设置</param>
        /// <returns>解析后的数据字典</returns>
        private static Dictionary<string, object> DecryptAndDeserialize(string json, StorageSettings settings)
        {
            if (string.IsNullOrEmpty(json))
            {
                return new Dictionary<string, object>();
            }

            // 解密处理
            if (settings.Encryption != StorageSettings.EncryptionType.None)
            {
                json = DecryptData(json, settings);
            }

            // 反序列化
            return StorageSerializer.DeserializeFromJson(json);
        }

        /// <summary>
        /// 根据存储位置保存数据
        /// </summary>
        /// <param name="json">JSON数据</param>
        /// <param name="settings">存储设置</param>
        /// <returns>是否成功</returns>
        private static bool SaveDataByLocation(string json, StorageSettings settings)
        {
            if (settings.Location == StorageSettings.StorageLocation.PlayerPrefs)
            {
                // 保存到PlayerPrefs必须在主线程执行
                if (Thread.CurrentThread.ManagedThreadId != _mainThreadId)
                {
                    NLogger.LogError("Cannot access PlayerPrefs from a background thread");
                    return false;
                }

                // 保存到PlayerPrefs
                PlayerPrefs.SetString(settings.FilePath, json);
                PlayerPrefs.Save();
                return true;
            }
            else if (settings.Location == StorageSettings.StorageLocation.File)
            {
                // 保存到文件
                WriteToFileWithBackup(json, settings);
                return true;
            }
            else
            {
                NLogger.LogError("Storage location {0} is not supported for writing", arg0: settings.Location);
                return false;
            }
        }

        /// <summary>
        /// 根据存储位置加载数据
        /// </summary>
        /// <param name="settings">存储设置</param>
        /// <returns>JSON字符串，如果失败则返回空字符串</returns>
        private static string LoadDataByLocation(StorageSettings settings)
        {
            if (settings.Location == StorageSettings.StorageLocation.PlayerPrefs)
            {
                // 从PlayerPrefs读取必须在主线程执行
                if (Thread.CurrentThread.ManagedThreadId != _mainThreadId)
                {
                    NLogger.LogError("Cannot access PlayerPrefs from a background thread");
                    return string.Empty;
                }

                // 从PlayerPrefs读取
                return PlayerPrefs.GetString(settings.FilePath, string.Empty);
            }
            else if (settings.Location == StorageSettings.StorageLocation.File)
            {
                // 从文件读取
                return ReadFromFileWithRecovery(settings);
            }
            else
            {
                NLogger.LogError("Storage location {0} is not supported for reading", arg0: settings.Location);
                return string.Empty;
            }
        }

        #endregion

        /// <summary>
        /// 加密数据
        /// </summary>
        /// <param name="data">要加密的数据</param>
        /// <param name="settings">设置对象</param>
        /// <returns>加密后的数据</returns>
        private static string EncryptData(string data, StorageSettings settings)
        {
            switch (settings.Encryption)
            {
                case StorageSettings.EncryptionType.AES:
                    return StorageEncryption.EncryptAES(data, settings.EncryptionPassword);
                case StorageSettings.EncryptionType.XOR:
                    return StorageEncryption.EncryptXOR(data, settings.EncryptionPassword);
                default:
                    return data;
            }
        }

        /// <summary>
        /// 解密数据
        /// </summary>
        /// <param name="encryptedData">加密的数据</param>
        /// <param name="settings">设置对象</param>
        /// <returns>解密后的数据</returns>
        private static string DecryptData(string encryptedData, StorageSettings settings)
        {
            switch (settings.Encryption)
            {
                case StorageSettings.EncryptionType.AES:
                    return StorageEncryption.DecryptAES(encryptedData, settings.EncryptionPassword);
                case StorageSettings.EncryptionType.XOR:
                    return StorageEncryption.DecryptXOR(encryptedData, settings.EncryptionPassword);
                default:
                    return encryptedData;
            }
        }

        /// <summary>
        /// 写入文件，使用安全备份机制
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="settings">设置对象</param>
        private static void WriteToFileWithBackup(string data, StorageSettings settings)
        {
            var filePath = settings.GetFullPath();
            var backupPath = settings.GetBackupPath();
            var directory = Path.GetDirectoryName(filePath);

            try
            {
                // 确保目录存在
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 先写入备份文件
                File.WriteAllText(backupPath, data, settings.TextEncoding);

                // 如果目标文件存在，先删除
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }

                // 将备份文件重命名为目标文件
                File.Move(backupPath, filePath);

                NLogger.Log("File saved successfully: {0}", arg0: filePath);
            }
            catch (Exception ex)
            {
                // 如果保存失败，清理备份文件
                if (File.Exists(backupPath))
                {
                    try
                    {
                        File.Delete(backupPath);
                    }
                    catch
                    {
                        // 忽略删除备份文件的错误
                    }
                }

                NLogger.LogError("Failed to save file: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                throw;
            }
        }

        /// <summary>
        /// 从文件读取，使用文件恢复机制
        /// </summary>
        /// <param name="settings">设置对象</param>
        /// <returns>文件内容</returns>
        private static string ReadFromFileWithRecovery(StorageSettings settings)
        {
            var filePath = settings.GetFullPath();
            var backupPath = settings.GetBackupPath();

            try
            {
                // 检查文件恢复情况
                CheckAndRecoverFile(filePath, backupPath);

                // 读取主文件
                if (File.Exists(filePath))
                {
                    return File.ReadAllText(filePath, settings.TextEncoding);
                }

                NLogger.LogWarning("File does not exist: {0}", arg0: filePath);
                return string.Empty;
            }
            catch (Exception ex)
            {
                NLogger.LogError("Failed to read file: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                return string.Empty;
            }
        }

        /// <summary>
        /// 检查并恢复文件
        /// </summary>
        /// <param name="filePath">主文件路径</param>
        /// <param name="backupPath">备份文件路径</param>
        private static void CheckAndRecoverFile(string filePath, string backupPath)
        {
            bool mainFileExists = File.Exists(filePath);
            bool backupFileExists = File.Exists(backupPath);

            if (!mainFileExists && backupFileExists)
            {
                // 主文件不存在但备份文件存在，说明上次保存过程中发生了崩溃
                try
                {
                    File.Move(backupPath, filePath);
                    NLogger.Log("Recovered file from backup: {0}", arg0: filePath);
                }
                catch (Exception ex)
                {
                    NLogger.LogError("Failed to recover file from backup: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                }
            }
            else if (mainFileExists && backupFileExists)
            {
                // 主文件和备份文件都存在，删除备份文件
                try
                {
                    File.Delete(backupPath);
                    NLogger.Log("Cleaned up backup file: {0}", arg0: backupPath);
                }
                catch (Exception ex)
                {
                    NLogger.LogWarning("Failed to clean up backup file: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                }
            }
        }

        #endregion

        #region 应用程序事件处理与同步文件操作

    /// <summary>
    /// 同步保存数据到文件
    /// </summary>
    /// <param name="settings">设置对象</param>
    /// <returns>是否成功</returns>
    public static bool SaveToFileSync(StorageSettings settings = null)
    {
        EnsureInitialized();

        // 性能优化：检查缓存是否有脏数据，如果没有，直接返回成功
        if (!Cache.IsDirty)
        {
            NLogger.Log("Cache is not dirty, skipping file save operation");
            return true;
        }

        var actualSettings = settings ?? DefaultSettings;
        var data = Cache.GetAllData();

        try
        {
            // 使用核心方法处理数据序列化和加密
            string json = SerializeAndEncrypt(data, actualSettings);

            // 使用核心方法根据存储位置保存数据
            bool success = SaveDataByLocation(json, actualSettings);

            if (success)
            {
                Cache.MarkAsSaved();
                NLogger.Log("Synchronously saved {0} items", arg0: data.Count);
            }
            else
            {
                NLogger.LogError("Failed to save data synchronously due to storage location error");
            }

            return success;
        }
        catch (Exception ex)
        {
            NLogger.LogError("Failed to save data synchronously: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
            return false;
        }
    }

    /// <summary>
    /// 同步从文件加载数据
    /// </summary>
    /// <param name="settings">设置对象</param>
    /// <returns>是否成功</returns>
    public static bool LoadFromFileSync(StorageSettings settings = null)
    {
        EnsureInitialized();

        var actualSettings = settings ?? DefaultSettings;

        try
        {
            // 使用核心方法根据存储位置加载数据
            string json = LoadDataByLocation(actualSettings);

            if (string.IsNullOrEmpty(json))
            {
                NLogger.LogWarning("File is empty or does not exist");
                return false;
            }

            // 使用核心方法处理数据解密和反序列化
            var data = DecryptAndDeserialize(json, actualSettings);

            // 加载到缓存
            Cache.LoadFromDictionary(data);
            NLogger.Log("Synchronously loaded {0} items", arg0: data.Count);
            return true;
        }
        catch (Exception ex)
        {
            NLogger.LogError("Failed to load data synchronously: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
            return false;
        }
    }

        /// <summary>
        /// 应用程序退出时的处理
        /// </summary>
        private static void OnApplicationQuitting()
        {
            if (_isInitialized && DefaultSettings.AutoSaveOnApplicationQuit && Cache.IsDirty)
            {
                // 在应用程序退出时使用同步保存方式，确保数据完全写入
                bool success = SaveToFileSync(DefaultSettings);
                if (success)
                {
                    NLogger.Log("Auto-saved data on application quit");
                }
                else
                {
                    NLogger.LogError("Failed to auto-save data on application quit");
                }
            }
        }

        /// <summary>
        /// 应用程序焦点变化时的处理
        /// </summary>
        /// <param name="hasFocus">是否有焦点</param>
        private static void OnApplicationFocusChanged(bool hasFocus)
        {
            if (_isInitialized && !hasFocus && DefaultSettings.AutoSaveOnApplicationPause && Cache.IsDirty)
            {
                // 在应用程序失去焦点时使用同步保存方式，确保数据完全写入
                bool success = SaveToFileSync(DefaultSettings);
                if (success)
                {
                    NLogger.Log("Auto-saved data on application pause");
                }
                else
                {
                    NLogger.LogError("Failed to auto-save data on application pause");
                }
            }
        }

        #endregion
    }
}
