using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using UnityEngine;
using Storage.Serialization;
using Storage.Encryption;
using DGame.Framework;

namespace Storage
{
    /// <summary>
    /// 文件操作完成回调委托
    /// </summary>
    /// <param name="success">操作是否成功</param>
    /// <param name="errorMessage">错误信息（如果有）</param>
    public delegate void FileOperationCallback(bool success, string errorMessage = null);

    /// <summary>
    /// 文件读取完成回调委托
    /// </summary>
    /// <param name="success">操作是否成功</param>
    /// <param name="data">读取的数据（如果成功）</param>
    /// <param name="errorMessage">错误信息（如果有）</param>
    public delegate void FileReadCallback(bool success, Dictionary<string, object> data = null, string errorMessage = null);

    /// <summary>
    /// 高性能数据持久化模块的主要API类
    /// 提供基于key-value的数据增删改查操作，支持内存缓存和文件持久化
    /// </summary>
    public static class Storage
    {
        #region 私有字段

        private static StorageCache _cache;
        private static StorageSettings _defaultSettings;
        private static bool _isInitialized = false;
        private static readonly Queue<Action> _mainThreadActions = new Queue<Action>();
        private static readonly object _mainThreadLock = new object();
        private static int _mainThreadId;

        #endregion

        #region 属性

        /// <summary>
        /// 默认设置
        /// </summary>
        public static StorageSettings DefaultSettings
        {
            get
            {
                if (_defaultSettings == null)
                {
                    _defaultSettings = new StorageSettings();
                }
                return _defaultSettings;
            }
            set
            {
                _defaultSettings = value;
            }
        }

        /// <summary>
        /// 内存缓存
        /// </summary>
        private static StorageCache Cache
        {
            get
            {
                if (_cache == null)
                {
                    _cache = new StorageCache();
                }
                return _cache;
            }
        }

        /// <summary>
        /// 是否已初始化
        /// </summary>
        public static bool IsInitialized
        {
            get { return _isInitialized; }
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化存储系统
        /// </summary>
        /// <param name="settings">设置对象</param>
        public static void Initialize(StorageSettings settings = null)
        {
            if (_isInitialized)
            {
                NLogger.LogWarning("Storage system is already initialized");
                return;
            }

            // 记录主线程ID
            _mainThreadId = Thread.CurrentThread.ManagedThreadId;

            _defaultSettings = settings ?? new StorageSettings();
            _cache = new StorageCache();

            // 注册应用程序事件
            RegisterApplicationEvents();

            _isInitialized = true;
            NLogger.Log("Storage system initialized successfully");
        }

        /// <summary>
        /// 注册应用程序事件
        /// </summary>
        private static void RegisterApplicationEvents()
        {
            Application.quitting += OnApplicationQuitting;
            Application.focusChanged += OnApplicationFocusChanged;
        }

        #endregion

        #region 数据操作方法

        /// <summary>
        /// 保存数据到内存缓存
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <param name="value">值</param>
        public static void Set<T>(string key, T value)
        {
            EnsureInitialized();

            if (string.IsNullOrEmpty(key))
            {
                NLogger.LogError("Key cannot be null or empty");
                return;
            }

            Cache.Set(key, value);
            NLogger.Log("Key: {0}, Type: {1}", arg0: key, arg1: typeof(T).Name);
        }

        /// <summary>
        /// 从内存缓存获取数据
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <returns>数据值</returns>
        public static T Get<T>(string key)
        {
            EnsureInitialized();

            if (string.IsNullOrEmpty(key))
            {
                NLogger.LogError("Key cannot be null or empty");
                return default;
            }

            return Cache.Get<T>(key);
        }

        /// <summary>
        /// 从内存缓存获取数据，如果不存在则返回默认值
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>数据值或默认值</returns>
        public static T Get<T>(string key, T defaultValue)
        {
            EnsureInitialized();

            if (string.IsNullOrEmpty(key))
            {
                NLogger.LogError("Key cannot be null or empty");
                return defaultValue;
            }

            return Cache.Get(key, defaultValue);
        }

        /// <summary>
        /// 检查键是否存在
        /// </summary>
        /// <param name="key">键</param>
        /// <returns>是否存在</returns>
        public static bool ContainsKey(string key)
        {
            EnsureInitialized();

            if (string.IsNullOrEmpty(key))
                return false;

            return Cache.ContainsKey(key);
        }

        /// <summary>
        /// 删除数据
        /// </summary>
        /// <param name="key">键</param>
        /// <returns>是否删除成功</returns>
        public static bool Remove(string key)
        {
            EnsureInitialized();

            if (string.IsNullOrEmpty(key))
            {
                NLogger.LogError("Key cannot be null or empty");
                return false;
            }

            return Cache.Remove(key);
        }

        /// <summary>
        /// 清空所有数据
        /// </summary>
        public static void Clear()
        {
            EnsureInitialized();
            Cache.Clear();
            NLogger.Log("All data cleared from cache");
        }

        /// <summary>
        /// 获取所有键
        /// </summary>
        /// <returns>键的集合</returns>
        public static ICollection<string> GetAllKeys()
        {
            EnsureInitialized();
            return Cache.Keys;
        }

        #endregion

        #region 文件操作方法

        /// <summary>
        /// 保存所有数据到文件
        /// </summary>
        /// <param name="settings">设置对象</param>
        /// <param name="callback">完成回调</param>
        public static void SaveToFile(StorageSettings settings = null, FileOperationCallback callback = null)
        {
            EnsureInitialized();

            var actualSettings = settings ?? DefaultSettings;

            // 在后台线程执行文件操作
            var thread = new Thread(() =>
            {
                try
                {
                    var data = Cache.GetAllData();

                    // 序列化
                    string json = StorageSerializer.SerializeToJson(data, actualSettings.PrettyPrint);

                    // 加密处理
                    if (actualSettings.Encryption != StorageSettings.EncryptionType.None)
                    {
                        json = EncryptData(json, actualSettings);
                    }

                    // 保存到文件
                    WriteToFile(json, actualSettings);

                    // 在主线程执行回调
                    RunOnMainThread(() =>
                    {
                        Cache.MarkAsSaved();
                        NLogger.Log("Saved {0} items to file: {1}", arg0: data.Count, arg1: actualSettings.GetFullPath());
                        callback?.Invoke(true);
                    });
                }
                catch (Exception ex)
                {
                    // 在主线程执行错误回调
                    RunOnMainThread(() =>
                    {
                        NLogger.LogError("Failed to save data: {0}", arg0: ex.Message);
                        callback?.Invoke(false, ex.Message);
                    });
                }
            })
            {
                Name = "Storage-SaveToFile",
                IsBackground = true
            };

            thread.Start();
        }

        /// <summary>
        /// 保存指定键的数据到文件
        /// </summary>
        /// <param name="keys">要保存的键列表</param>
        /// <param name="settings">设置对象</param>
        /// <param name="callback">完成回调</param>
        public static void SaveKeysToFile(IEnumerable<string> keys, StorageSettings settings = null, FileOperationCallback callback = null)
        {
            EnsureInitialized();

            if (keys == null)
            {
                NLogger.LogError("Keys collection cannot be null");
                callback?.Invoke(false, "Keys collection cannot be null");
                return;
            }

            var actualSettings = settings ?? DefaultSettings;

            // 在后台线程执行文件操作
            var thread = new Thread(() =>
            {
                try
                {
                    var dataToSave = new Dictionary<string, object>();

                    foreach (var key in keys)
                    {
                        if (Cache.ContainsKey(key))
                        {
                            dataToSave[key] = Cache.Get<object>(key);
                        }
                    }

                    if (dataToSave.Count == 0)
                    {
                        RunOnMainThread(() =>
                        {
                            NLogger.LogWarning("No valid keys found to save");
                            callback?.Invoke(false, "No valid keys found to save");
                        });
                        return;
                    }

                    // 序列化
                    string json = StorageSerializer.SerializeToJson(dataToSave, actualSettings.PrettyPrint);

                    // 加密处理
                    if (actualSettings.Encryption != StorageSettings.EncryptionType.None)
                    {
                        json = EncryptData(json, actualSettings);
                    }

                    // 保存到文件
                    WriteToFile(json, actualSettings);

                    // 在主线程执行回调
                    RunOnMainThread(() =>
                    {
                        NLogger.Log("Saved {0} items to file: {1}", arg0: dataToSave.Count, arg1: actualSettings.GetFullPath());
                        callback?.Invoke(true);
                    });
                }
                catch (Exception ex)
                {
                    // 在主线程执行错误回调
                    RunOnMainThread(() =>
                    {
                        NLogger.LogError("Failed to save data: {0}", arg0: ex.Message);
                        callback?.Invoke(false, ex.Message);
                    });
                }
            })
            {
                Name = "Storage-SaveKeysToFile",
                IsBackground = true
            };

            thread.Start();
        }

        /// <summary>
        /// 从文件加载数据
        /// </summary>
        /// <param name="settings">设置对象</param>
        /// <param name="callback">完成回调</param>
        public static void LoadFromFile(StorageSettings settings = null, FileReadCallback callback = null)
        {
            EnsureInitialized();

            var actualSettings = settings ?? DefaultSettings;

            // 在后台线程执行文件操作
            var thread = new Thread(() =>
            {
                try
                {
                    // 读取文件
                    string json = ReadFromFile(actualSettings);

                    if (string.IsNullOrEmpty(json))
                    {
                        RunOnMainThread(() =>
                        {
                            NLogger.LogWarning("File is empty or does not exist: {0}", arg0: actualSettings.GetFullPath());
                            callback?.Invoke(false, null, "File is empty or does not exist");
                        });
                        return;
                    }

                    // 解密处理
                    if (actualSettings.Encryption != StorageSettings.EncryptionType.None)
                    {
                        json = DecryptData(json, actualSettings);
                    }

                    // 反序列化
                    var data = StorageSerializer.DeserializeFromJson(json);

                    // 在主线程执行回调
                    RunOnMainThread(() =>
                    {
                        Cache.LoadFromDictionary(data);
                        NLogger.Log("Loaded {0} items from file: {1}", arg0: data.Count, arg1: actualSettings.GetFullPath());
                        callback?.Invoke(true, data);
                    });
                }
                catch (Exception ex)
                {
                    // 在主线程执行错误回调
                    RunOnMainThread(() =>
                    {
                        NLogger.LogError("Failed to load data: {0}", arg0: ex.Message);
                        callback?.Invoke(false, null, ex.Message);
                    });
                }
            })
            {
                Name = "Storage-LoadFromFile",
                IsBackground = true
            };

            thread.Start();
        }

        /// <summary>
        /// 检查文件是否存在
        /// </summary>
        /// <param name="settings">设置对象</param>
        /// <returns>文件是否存在</returns>
        public static bool FileExists(StorageSettings settings = null)
        {
            var actualSettings = settings ?? DefaultSettings;

            switch (actualSettings.Location)
            {
                case StorageSettings.StorageLocation.File:
                    return File.Exists(actualSettings.GetFullPath());
                case StorageSettings.StorageLocation.PlayerPrefs:
                    return PlayerPrefs.HasKey(actualSettings.FilePath);
                default:
                    return false;
            }
        }

        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="settings">设置对象</param>
        public static void DeleteFile(StorageSettings settings = null)
        {
            var actualSettings = settings ?? DefaultSettings;

            try
            {
                switch (actualSettings.Location)
                {
                    case StorageSettings.StorageLocation.File:
                        if (File.Exists(actualSettings.GetFullPath()))
                        {
                            File.Delete(actualSettings.GetFullPath());
                            NLogger.Log("File deleted: {0}", arg0: actualSettings.GetFullPath());
                        }
                        // 同时删除可能存在的备份文件
                        if (File.Exists(actualSettings.GetBackupPath()))
                        {
                            File.Delete(actualSettings.GetBackupPath());
                            NLogger.Log("Backup file deleted: {0}", arg0: actualSettings.GetBackupPath());
                        }
                        break;
                    case StorageSettings.StorageLocation.PlayerPrefs:
                        PlayerPrefs.DeleteKey(actualSettings.FilePath);
                        NLogger.Log("PlayerPrefs key deleted: {0}", arg0: actualSettings.FilePath);
                        break;
                }
            }
            catch (Exception ex)
            {
                NLogger.LogError("Failed to delete file: {0}", arg0: ex.Message);
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 确保系统已初始化
        /// </summary>
        private static void EnsureInitialized()
        {
            if (!_isInitialized)
            {
                Initialize();
            }
        }

        /// <summary>
        /// 在主线程执行操作
        /// </summary>
        /// <param name="action">要执行的操作</param>
        private static void RunOnMainThread(Action action)
        {
            if (Thread.CurrentThread.ManagedThreadId == _mainThreadId)
            {
                // 已经在主线程，直接执行
                action();
            }
            else
            {
                // 在后台线程，需要调度到主线程
                lock (_mainThreadLock)
                {
                    _mainThreadActions.Enqueue(action);
                }
            }
        }

        /// <summary>
        /// 处理主线程队列中的操作（需要在主线程的Update中调用）
        /// </summary>
        public static void ProcessMainThreadActions()
        {
            if (Thread.CurrentThread.ManagedThreadId != _mainThreadId)
            {
                return; // 只能在主线程调用
            }

            lock (_mainThreadLock)
            {
                while (_mainThreadActions.Count > 0)
                {
                    var action = _mainThreadActions.Dequeue();
                    try
                    {
                        action();
                    }
                    catch (Exception ex)
                    {
                        NLogger.LogError("Error executing main thread action: {0}", arg0: ex.Message);
                    }
                }
            }
        }

        /// <summary>
        /// 加密数据
        /// </summary>
        /// <param name="data">要加密的数据</param>
        /// <param name="settings">设置对象</param>
        /// <returns>加密后的数据</returns>
        private static string EncryptData(string data, StorageSettings settings)
        {
            switch (settings.Encryption)
            {
                case StorageSettings.EncryptionType.AES:
                    return StorageEncryption.EncryptAES(data, settings.EncryptionPassword);
                case StorageSettings.EncryptionType.XOR:
                    return StorageEncryption.EncryptXOR(data, settings.EncryptionPassword);
                default:
                    return data;
            }
        }

        /// <summary>
        /// 解密数据
        /// </summary>
        /// <param name="encryptedData">加密的数据</param>
        /// <param name="settings">设置对象</param>
        /// <returns>解密后的数据</returns>
        private static string DecryptData(string encryptedData, StorageSettings settings)
        {
            switch (settings.Encryption)
            {
                case StorageSettings.EncryptionType.AES:
                    return StorageEncryption.DecryptAES(encryptedData, settings.EncryptionPassword);
                case StorageSettings.EncryptionType.XOR:
                    return StorageEncryption.DecryptXOR(encryptedData, settings.EncryptionPassword);
                default:
                    return encryptedData;
            }
        }

        /// <summary>
        /// 写入文件（带安全备份机制）
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="settings">设置对象</param>
        private static void WriteToFile(string data, StorageSettings settings)
        {
            switch (settings.Location)
            {
                case StorageSettings.StorageLocation.File:
                    WriteToFileWithBackup(data, settings);
                    break;
                case StorageSettings.StorageLocation.PlayerPrefs:
                    PlayerPrefs.SetString(settings.FilePath, data);
                    PlayerPrefs.Save();
                    break;
                default:
                    throw new NotSupportedException($"Storage location {settings.Location} is not supported for writing");
            }
        }

        /// <summary>
        /// 写入文件，使用安全备份机制
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="settings">设置对象</param>
        private static void WriteToFileWithBackup(string data, StorageSettings settings)
        {
            var filePath = settings.GetFullPath();
            var backupPath = settings.GetBackupPath();
            var directory = Path.GetDirectoryName(filePath);

            try
            {
                // 确保目录存在
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 先写入备份文件
                File.WriteAllText(backupPath, data, settings.TextEncoding);

                // 如果目标文件存在，先删除
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }

                // 将备份文件重命名为目标文件
                File.Move(backupPath, filePath);

                NLogger.Log("File saved successfully: {0}", arg0: filePath);
            }
            catch (Exception ex)
            {
                // 如果保存失败，清理备份文件
                if (File.Exists(backupPath))
                {
                    try
                    {
                        File.Delete(backupPath);
                    }
                    catch
                    {
                        // 忽略删除备份文件的错误
                    }
                }

                NLogger.LogError("Failed to save file: {0}", arg0: ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 从文件读取（带文件恢复机制）
        /// </summary>
        /// <param name="settings">设置对象</param>
        /// <returns>文件内容</returns>
        private static string ReadFromFile(StorageSettings settings)
        {
            switch (settings.Location)
            {
                case StorageSettings.StorageLocation.File:
                    return ReadFromFileWithRecovery(settings);
                case StorageSettings.StorageLocation.PlayerPrefs:
                    return PlayerPrefs.GetString(settings.FilePath, string.Empty);
                default:
                    throw new NotSupportedException($"Storage location {settings.Location} is not supported for reading");
            }
        }

        /// <summary>
        /// 从文件读取，使用文件恢复机制
        /// </summary>
        /// <param name="settings">设置对象</param>
        /// <returns>文件内容</returns>
        private static string ReadFromFileWithRecovery(StorageSettings settings)
        {
            var filePath = settings.GetFullPath();
            var backupPath = settings.GetBackupPath();

            try
            {
                // 检查文件恢复情况
                CheckAndRecoverFile(filePath, backupPath);

                // 读取主文件
                if (File.Exists(filePath))
                {
                    return File.ReadAllText(filePath, settings.TextEncoding);
                }

                NLogger.LogWarning("File does not exist: {0}", arg0: filePath);
                return string.Empty;
            }
            catch (Exception ex)
            {
                NLogger.LogError("Failed to read file: {0}", arg0: ex.Message);
                return string.Empty;
            }
        }

        /// <summary>
        /// 检查并恢复文件
        /// </summary>
        /// <param name="filePath">主文件路径</param>
        /// <param name="backupPath">备份文件路径</param>
        private static void CheckAndRecoverFile(string filePath, string backupPath)
        {
            bool mainFileExists = File.Exists(filePath);
            bool backupFileExists = File.Exists(backupPath);

            if (!mainFileExists && backupFileExists)
            {
                // 主文件不存在但备份文件存在，说明上次保存过程中发生了崩溃
                try
                {
                    File.Move(backupPath, filePath);
                    NLogger.Log("Recovered file from backup: {0}", arg0: filePath);
                }
                catch (Exception ex)
                {
                    NLogger.LogError("Failed to recover file from backup: {0}", arg0: ex.Message);
                }
            }
            else if (mainFileExists && backupFileExists)
            {
                // 主文件和备份文件都存在，删除备份文件
                try
                {
                    File.Delete(backupPath);
                    NLogger.Log("Cleaned up backup file: {0}", arg0: backupPath);
                }
                catch (Exception ex)
                {
                    NLogger.LogWarning("Failed to clean up backup file: {0}", arg0: ex.Message);
                }
            }
        }

        #endregion

        #region 应用程序事件处理

        /// <summary>
        /// 应用程序退出时的处理
        /// </summary>
        private static void OnApplicationQuitting()
        {
            if (_isInitialized && DefaultSettings.AutoSaveOnApplicationQuit && Cache.IsDirty)
            {
                SaveToFile(null, (success, errorMessage) =>
                {
                    if (success)
                    {
                        NLogger.Log("Auto-saved data on application quit");
                    }
                    else
                    {
                        NLogger.LogError("Failed to auto-save data on application quit: {0}", arg0: errorMessage);
                    }
                });
            }
        }

        /// <summary>
        /// 应用程序焦点变化时的处理
        /// </summary>
        /// <param name="hasFocus">是否有焦点</param>
        private static void OnApplicationFocusChanged(bool hasFocus)
        {
            if (_isInitialized && !hasFocus && DefaultSettings.AutoSaveOnApplicationPause && Cache.IsDirty)
            {
                SaveToFile(null, (success, errorMessage) =>
                {
                    if (success)
                    {
                        NLogger.Log("Auto-saved data on application pause");
                    }
                    else
                    {
                        NLogger.LogError("Failed to auto-save data on application pause: {0}", arg0: errorMessage);
                    }
                });
            }
        }

        #endregion
    }
}
