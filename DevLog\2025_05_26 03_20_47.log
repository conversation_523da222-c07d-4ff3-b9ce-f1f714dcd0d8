2025-05-26 03:20:47.3495 [INFO] [StorageSettings]::InitializePathCache(87) - Storage paths cached successfully
2025-05-26 03:20:47.3713 [INFO] [Storage]::Initialize(128) - Storage system initialized successfully
2025-05-26 03:20:47.3713 [INFO] [StorageCache]::Set(87) - Key: test_string, Type: String
2025-05-26 03:20:47.3713 [INFO] [Storage]::Set(161) - Key: test_string, Type: String
2025-05-26 03:20:47.3713 [INFO] [StorageCache]::Set(87) - Key: test_int, Type: Int32
2025-05-26 03:20:47.3713 [INFO] [Storage]::Set(161) - Key: test_int, Type: Int32
2025-05-26 03:20:47.3713 [INFO] [StorageCache]::Set(87) - Key: test_float, Type: Single
2025-05-26 03:20:47.3713 [INFO] [Storage]::Set(161) - Key: test_float, Type: Single
2025-05-26 03:20:47.3713 [INFO] [StorageCache]::Set(87) - Key: test_bool, Type: Boolean
2025-05-26 03:20:47.3713 [INFO] [Storage]::Set(161) - Key: test_bool, Type: Boolean
2025-05-26 03:20:47.4126 [INFO] [Storage]::WriteToFileWithBackup(771) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\SaveData.json
2025-05-26 03:20:47.8402 [INFO] [Storage]::SaveToFileInBackground(352) - Saved 4 items to file: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\SaveData.json
2025-05-26 03:20:47.8402 [INFO] [StorageCache]::Clear(199) - Cleared 4 items from cache
2025-05-26 03:20:47.8402 [INFO] [Storage]::Clear(243) - All data cleared from cache
2025-05-26 03:20:48.0970 [INFO] [StorageCache]::LoadFromDictionary(234) - Loaded 4 items into cache
2025-05-26 03:20:48.0970 [INFO] [Storage]::LoadFromFileInBackground(560) - Loaded 4 items from file: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\SaveData.json
2025-05-26 03:20:48.0970 [ERROR] [StorageCache]::Get(112) - Cannot cast value for key 'test_int' to type Int32
StackTrace:   at Storage.StorageCache.Get[T] (System.String key) [0x00048] in H:\DiceGame\DGame\Assets\Scripts\Storage\Core\StorageCache.cs:108 
2025-05-26 03:20:48.0970 [ERROR] [StorageCache]::Get(112) - Cannot cast value for key 'test_float' to type Single
StackTrace:   at Storage.StorageCache.Get[T] (System.String key) [0x00048] in H:\DiceGame\DGame\Assets\Scripts\Storage\Core\StorageCache.cs:108 
