using UnityEngine;
using Storage;

namespace Storage
{
    /// <summary>
    /// Storage模块测试脚本
    /// 用于测试Thread模式的文件操作
    /// </summary>
    public class StorageTest : MonoBehaviour
    {
        private void Start()
        {
            // 初始化Storage系统
            Storage.Initialize();

            // 测试保存数据
            TestSaveData();
        }

        private void TestSaveData()
        {
            // 设置一些测试数据
            Storage.Set("test_string", "Hello World");
            Storage.Set("test_int", 42);
            Storage.Set("test_float", 3.14f);
            Storage.Set("test_bool", true);

            Debug.Log("设置测试数据完成");

            // 异步保存到文件
            Storage.SaveToFile(null, (success, errorMessage) =>
            {
                if (success)
                {
                    Debug.Log("数据保存成功！");
                    TestLoadData();
                }
                else
                {
                    Debug.LogError($"数据保存失败: {errorMessage}");
                }
            });
        }

        private void TestLoadData()
        {
            // 清空缓存
            Storage.Clear();
            Debug.Log("清空缓存完成");

            // 异步从文件加载
            Storage.LoadFromFile(null, (success, data, errorMessage) =>
            {
                if (success)
                {
                    Debug.Log($"数据加载成功！加载了 {data?.Count ?? 0} 项数据");

                    // 验证数据
                    var testString = Storage.Get<string>("test_string");
                    var testInt = Storage.Get<int>("test_int");
                    var testFloat = Storage.Get<float>("test_float");
                    var testBool = Storage.Get<bool>("test_bool");

                    Debug.Log($"test_string: {testString}");
                    Debug.Log($"test_int: {testInt}");
                    Debug.Log($"test_float: {testFloat}");
                    Debug.Log($"test_bool: {testBool}");
                }
                else
                {
                    Debug.LogError($"数据加载失败: {errorMessage}");
                }
            });
        }
    }
}
