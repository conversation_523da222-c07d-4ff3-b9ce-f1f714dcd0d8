2025-05-26 02:51:37.4098 [INFO] [Storage]::Initialize(117) - Storage system initialized successfully
2025-05-26 02:51:37.4250 [INFO] [StorageCache]::Set(87) - Key: test_string, Type: String
2025-05-26 02:51:37.4250 [INFO] [Storage]::Set(150) - Key: test_string, Type: String
2025-05-26 02:51:37.4250 [INFO] [StorageCache]::Set(87) - Key: test_int, Type: Int32
2025-05-26 02:51:37.4250 [INFO] [Storage]::Set(150) - Key: test_int, Type: Int32
2025-05-26 02:51:37.4250 [INFO] [StorageCache]::Set(87) - Key: test_float, Type: Single
2025-05-26 02:51:37.4250 [INFO] [Storage]::Set(150) - Key: test_float, Type: Single
2025-05-26 02:51:37.4250 [INFO] [StorageCache]::Set(87) - Key: test_bool, Type: Boolean
2025-05-26 02:51:37.4250 [INFO] [Storage]::Set(150) - Key: test_bool, Type: Boolean
2025-05-26 02:51:38.1416 [ERROR] [Storage]::SaveToFile(292) - Failed to save data: get_persistentDataPath can only be called from the main thread.
Constructors and field initializers will be executed from the loading thread when loading a scene.
Don't use this function in the constructor or field initializers, instead move initialization code to the Awake or Start function.
StackTrace:   at (wrapper managed-to-native) UnityEngine.Application.get_persistentDataPath()
  at Storage.StorageSettings.GetFullPath () [0x00054] in H:\DiceGame\DGame\Assets\Scripts\Storage\Core\StorageSettings.cs:242 
  at Storage.Storage.WriteToFileWithBackup (System.String data, Storage.StorageSettings settings) [0x00001] in H:\DiceGame\DGame\Assets\Scripts\Storage\Storage.cs:636 
  at Storage.Storage.WriteToFile (System.String data, Storage.StorageSettings settings) [0x00015] in H:\DiceGame\DGame\Assets\Scripts\Storage\Storage.cs:618 
  at Storage.Storage+<>c__DisplayClass22_0.<SaveToFile>b__0 () [0x00057] in H:\DiceGame\DGame\Assets\Scripts\Storage\Storage.cs:277 
2025-05-26 02:51:49.8278 [ERROR] [Storage]::SaveToFile(292) - Failed to save data: get_persistentDataPath can only be called from the main thread.
Constructors and field initializers will be executed from the loading thread when loading a scene.
Don't use this function in the constructor or field initializers, instead move initialization code to the Awake or Start function.
StackTrace:   at (wrapper managed-to-native) UnityEngine.Application.get_persistentDataPath()
  at Storage.StorageSettings.GetFullPath () [0x00054] in H:\DiceGame\DGame\Assets\Scripts\Storage\Core\StorageSettings.cs:242 
  at Storage.Storage.WriteToFileWithBackup (System.String data, Storage.StorageSettings settings) [0x00001] in H:\DiceGame\DGame\Assets\Scripts\Storage\Storage.cs:636 
  at Storage.Storage.WriteToFile (System.String data, Storage.StorageSettings settings) [0x00015] in H:\DiceGame\DGame\Assets\Scripts\Storage\Storage.cs:618 
  at Storage.Storage+<>c__DisplayClass22_0.<SaveToFile>b__0 () [0x00057] in H:\DiceGame\DGame\Assets\Scripts\Storage\Storage.cs:277 
2025-05-26 02:51:49.8278 [ERROR] [Storage]::OnApplicationFocusChanged(810) - Failed to auto-save data on application pause: get_persistentDataPath can only be called from the main thread.
Constructors and field initializers will be executed from the loading thread when loading a scene.
Don't use this function in the constructor or field initializers, instead move initialization code to the Awake or Start function.
