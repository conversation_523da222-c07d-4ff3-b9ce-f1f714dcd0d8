Using pre-set license
Built from '2022.3/china_unity/release' branch; Version is '2022.3.57f1c2 (32588f90613b) revision 3299471'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'zh' Physical Memory: 32686 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
E:\Program Files\Unity\Editor\2022.3.57f1c2\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
H:/DiceGame/DGame
-logFile
Logs/AssetImportWorker1.log
-srvPort
51316
Successfully changed project path to: H:/DiceGame/DGame
H:/DiceGame/DGame
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [35868]  Target information:

Player connection [35868]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 866573162 [EditorId] 866573162 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-31EMADL) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [35868]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 866573162 [EditorId] 866573162 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-31EMADL) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [35868]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 866573162 [EditorId] 866573162 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-31EMADL) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [35868] Host joined multi-casting on [***********:54997]...
Player connection [35868] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 32 workers.
Refreshing native plugins compatible for Editor in 10.80 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.57f1c2 (32588f90613b)
[Subsystems] Discovering subsystems at path E:/Program Files/Unity/Editor/2022.3.57f1c2/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path H:/DiceGame/DGame/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3080 (ID=0x2206)
    Vendor:   NVIDIA
    VRAM:     10053 MB
    Driver:   32.0.15.7652
Initialize mono
Mono path[0] = 'E:/Program Files/Unity/Editor/2022.3.57f1c2/Editor/Data/Managed'
Mono path[1] = 'E:/Program Files/Unity/Editor/2022.3.57f1c2/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Program Files/Unity/Editor/2022.3.57f1c2/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56584
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Program Files/Unity/Editor/2022.3.57f1c2/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002796 seconds.
- Loaded All Assemblies, in  0.239 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.208 seconds
Domain Reload Profiling: 447ms
	BeginReloadAssembly (77ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (94ms)
		LoadAssemblies (76ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (92ms)
			TypeCache.Refresh (91ms)
				TypeCache.ScanAssembly (83ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (208ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (173ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (120ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.605 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.676 seconds
Domain Reload Profiling: 1281ms
	BeginReloadAssembly (103ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (16ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (420ms)
		LoadAssemblies (292ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (187ms)
			TypeCache.Refresh (155ms)
				TypeCache.ScanAssembly (141ms)
			ScanForSourceGeneratedMonoScriptInfo (22ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (676ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (550ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (107ms)
			ProcessInitializeOnLoadAttributes (364ms)
			ProcessInitializeOnLoadMethodAttributes (66ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.05 seconds
Refreshing native plugins compatible for Editor in 5.10 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6083 Unused Serialized files (Serialized files now loaded: 0)
Unloading 42 unused Assets / (349.9 KB). Loaded Objects now: 6678.
Memory consumption went from 234.4 MB to 234.1 MB.
Total: 3.339200 ms (FindLiveObjects: 0.334700 ms CreateObjectMapping: 0.245600 ms MarkObjects: 2.629400 ms  DeleteObjects: 0.128800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.832 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.660 seconds
Domain Reload Profiling: 1494ms
	BeginReloadAssembly (511ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (390ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (253ms)
		LoadAssemblies (275ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (36ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (17ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (661ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (517ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (106ms)
			ProcessInitializeOnLoadAttributes (343ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 7.99 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5949 Unused Serialized files (Serialized files now loaded: 0)
Unloading 30 unused Assets / (320.6 KB). Loaded Objects now: 6719.
Memory consumption went from 205.7 MB to 205.3 MB.
Total: 3.489800 ms (FindLiveObjects: 0.342800 ms CreateObjectMapping: 0.293800 ms MarkObjects: 2.772900 ms  DeleteObjects: 0.079600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SkewTextExample.cs: c142d02d70a129e0533975f7c2f96956 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/TMP_TextEventHandler.cs: f5cd9d5a43bfefc8f4fec80e6de896a4 -> 
  custom:scripting/monoscript/fileName/TextMeshProFloatingText.cs: 92608fa4a6315cd0786d82205e77b697 -> 
  custom:scripting/monoscript/fileName/VertexZoom.cs: 650ab1e81d40126e3edcf737cce646e4 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/VertexShakeA.cs: 63ec564c588058369499638dd899e05b -> 
  custom:scripting/monoscript/fileName/TMP_ExampleScript_01.cs: a9d4be556104ff54e7d240529b3e01dc -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/TMP_TextEventCheck.cs: 13505d917eab5868c435e0c2986dadfa -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_B.cs: ba2be21a7486d37b1011342977a2e49d -> 
  custom:scripting/monoscript/fileName/Benchmark01.cs: 2d3e2cd961730ab95f139a49a673bace -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/Benchmark02.cs: 5640963252ce70910e726415f869ced6 -> 
  custom:scripting/monoscript/fileName/DropdownSample.cs: 0a92251794e5f10400149f96c3c59e5d -> 
  custom:scripting/monoscript/fileName/WarpTextExample.cs: 2663459c266363641981c7d17b698d7f -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/ChatController.cs: 876d5ccae6e345dd5d9450eb0a748f16 -> 
  custom:scripting/monoscript/fileName/TextMeshSpawner.cs: 76f28e2ddb031571e6ec62b018f65fa8 -> 
  custom:scripting/monoscript/fileName/TeleType.cs: 31fa97821c4c70f157ec81d507aa6b62 -> 
  custom:scripting/monoscript/fileName/Benchmark04.cs: 32220b148620e52aa81c13a114862bf4 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/TMP_PhoneNumberValidator.cs: 7c3049f481e464c6a9a15dee56c3126f -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/EnvMapAnimator.cs: 1b4f61c17e4b7283c3ac351aacff9d26 -> 
  custom:scripting/monoscript/fileName/ShaderPropAnimator.cs: 55ce425812446482e623ff2695c94cb2 -> 
  custom:scripting/monoscript/fileName/SimpleScript.cs: 454caab0c6c050f29b39d82e1d66d0fa -> 
  custom:scripting/monoscript/fileName/ObjectSpin.cs: 2b96bb2079dd7fa5108d981627a28e1e -> 
  custom:scripting/monoscript/fileName/CameraController.cs: 1daac0040b3fce2177f6ca4f90eb8c5b -> 90f26c7c63924735c23c826b8a781463
  custom:scripting/monoscript/fileName/TMP_TextInfoDebugTool.cs: 3f1709cfb2cc628169113a35b5e9be3f -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_A.cs: 347309a09d56e367b188a6978f75b424 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/VertexJitter.cs: d27eb5498e48714a66aece98da065133 -> 
  custom:scripting/monoscript/fileName/TextConsoleSimulator.cs: 33fc311abb20b5b0ed8f00e4d86b574e -> 
  custom:scripting/monoscript/fileName/VertexColorCycler.cs: 5f984ca91cf3609293aecc6bc5641572 -> 
  custom:scripting/monoscript/fileName/TMP_FrameRateCounter.cs: 46f655df8cb483ac5958307137b1585a -> 
  custom:scripting/monoscript/fileName/VertexShakeB.cs: a6f78c4fcda1a29a1a1c1baefedba9e7 -> 
  custom:scripting/monoscript/fileName/TMP_UiFrameRateCounter.cs: b01021aea19247f14a98ab338907956a -> 
  custom:scripting/monoscript/fileName/TMPro_InstructionOverlay.cs: b626d2270e6ebd5c4311585c18b58d8d -> 
  custom:scripting/monoscript/fileName/TMP_DigitValidator.cs: ac02af96215ae3951a3cce9e1a8c6085 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/Benchmark01_UGUI.cs: 6148cfe2627b7708298f301788144139 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 1451866cbca9d4f56315bf672c4629ce -> 66c59ad6cee36014c710f4c18c6650dc
  custom:scripting/monoscript/fileName/Benchmark03.cs: 17cc87e0529ac71d909a946afb47a274 -> 
========================================================================
Received Import Request.
  Time since last request: 5165.660329 seconds.
  path: Assets/Plugins/Easy Save 3/Editor/checkmarkSmall.png
  artifactKey: Guid(ca44f6eb870d244cfb1c83054a69ffc3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Easy Save 3/Editor/checkmarkSmall.png using Guid(ca44f6eb870d244cfb1c83054a69ffc3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 32 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 32 workers.
 -> (artifact id: '86a95b6eddfaf4521d80bb7f4d4b8603') in 0.043926 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.633 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.127 seconds
Domain Reload Profiling: 1761ms
	BeginReloadAssembly (183ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (67ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (375ms)
		LoadAssemblies (403ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (40ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (19ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1128ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (553ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (106ms)
			ProcessInitializeOnLoadAttributes (371ms)
			ProcessInitializeOnLoadMethodAttributes (63ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 7.43 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5956 Unused Serialized files (Serialized files now loaded: 0)
Unloading 30 unused Assets / (319.6 KB). Loaded Objects now: 6735.
Memory consumption went from 205.8 MB to 205.5 MB.
Total: 4.161800 ms (FindLiveObjects: 0.354100 ms CreateObjectMapping: 0.295300 ms MarkObjects: 3.432700 ms  DeleteObjects: 0.078900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SkewTextExample.cs: c142d02d70a129e0533975f7c2f96956 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/TMP_TextEventHandler.cs: f5cd9d5a43bfefc8f4fec80e6de896a4 -> 
  custom:scripting/monoscript/fileName/TextMeshProFloatingText.cs: 92608fa4a6315cd0786d82205e77b697 -> 
  custom:scripting/monoscript/fileName/VertexZoom.cs: 650ab1e81d40126e3edcf737cce646e4 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/VertexShakeA.cs: 63ec564c588058369499638dd899e05b -> 
  custom:scripting/monoscript/fileName/TMP_ExampleScript_01.cs: a9d4be556104ff54e7d240529b3e01dc -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/TMP_TextEventCheck.cs: 13505d917eab5868c435e0c2986dadfa -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_B.cs: ba2be21a7486d37b1011342977a2e49d -> 
  custom:scripting/monoscript/fileName/Benchmark01.cs: 2d3e2cd961730ab95f139a49a673bace -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/Benchmark02.cs: 5640963252ce70910e726415f869ced6 -> 
  custom:scripting/monoscript/fileName/DropdownSample.cs: 0a92251794e5f10400149f96c3c59e5d -> 
  custom:scripting/monoscript/fileName/WarpTextExample.cs: 2663459c266363641981c7d17b698d7f -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/StorageSerializer.cs: bd2e1f85a8c0c08db143374b1c4b7bc5 -> 
  custom:scripting/monoscript/fileName/ChatController.cs: 876d5ccae6e345dd5d9450eb0a748f16 -> 
  custom:scripting/monoscript/fileName/TextMeshSpawner.cs: 76f28e2ddb031571e6ec62b018f65fa8 -> 
  custom:scripting/monoscript/fileName/TeleType.cs: 31fa97821c4c70f157ec81d507aa6b62 -> 
  custom:scripting/monoscript/fileName/Benchmark04.cs: 32220b148620e52aa81c13a114862bf4 -> 
  custom:scripting/monoscript/fileName/Storage.cs: 0d55ff6f69c85da5c119baec6b4da9c9 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/TMP_PhoneNumberValidator.cs: 7c3049f481e464c6a9a15dee56c3126f -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/EnvMapAnimator.cs: 1b4f61c17e4b7283c3ac351aacff9d26 -> 
  custom:scripting/monoscript/fileName/ShaderPropAnimator.cs: 55ce425812446482e623ff2695c94cb2 -> 
  custom:scripting/monoscript/fileName/SimpleScript.cs: 454caab0c6c050f29b39d82e1d66d0fa -> 
  custom:scripting/monoscript/fileName/ObjectSpin.cs: 2b96bb2079dd7fa5108d981627a28e1e -> 
  custom:scripting/monoscript/fileName/CameraController.cs: 1daac0040b3fce2177f6ca4f90eb8c5b -> 90f26c7c63924735c23c826b8a781463
  custom:scripting/monoscript/fileName/TMP_TextInfoDebugTool.cs: 3f1709cfb2cc628169113a35b5e9be3f -> 
  custom:scripting/monoscript/fileName/StorageExample.cs: fbf56fbbe4a92ff023ad02de60c4da0e -> 
  custom:scripting/monoscript/fileName/StorageEncryption.cs: 1665512cf453609b5d2b62e0a4819a91 -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_A.cs: 347309a09d56e367b188a6978f75b424 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/StorageSettings.cs: 35ec0a7219481e610f2bc52f370f6422 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/VertexJitter.cs: d27eb5498e48714a66aece98da065133 -> 
  custom:scripting/monoscript/fileName/TextConsoleSimulator.cs: 33fc311abb20b5b0ed8f00e4d86b574e -> 
  custom:scripting/monoscript/fileName/VertexColorCycler.cs: 5f984ca91cf3609293aecc6bc5641572 -> 
  custom:scripting/monoscript/fileName/TMP_FrameRateCounter.cs: 46f655df8cb483ac5958307137b1585a -> 
  custom:scripting/monoscript/fileName/VertexShakeB.cs: a6f78c4fcda1a29a1a1c1baefedba9e7 -> 
  custom:scripting/monoscript/fileName/TMP_UiFrameRateCounter.cs: b01021aea19247f14a98ab338907956a -> 
  custom:scripting/monoscript/fileName/TMPro_InstructionOverlay.cs: b626d2270e6ebd5c4311585c18b58d8d -> 
  custom:scripting/monoscript/fileName/StorageCache.cs: 73093796b6294aaac7481c89ba959971 -> 
  custom:scripting/monoscript/fileName/TMP_DigitValidator.cs: ac02af96215ae3951a3cce9e1a8c6085 -> 
  custom:scripting/monoscript/fileName/UnityJsonConverters.cs: 801e98db15bb29b0fbdc94637601d63e -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/Benchmark01_UGUI.cs: 6148cfe2627b7708298f301788144139 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 18d849333c205e92cd3e4fbcf0988960 -> 66c59ad6cee36014c710f4c18c6650dc
  custom:scripting/monoscript/fileName/Benchmark03.cs: 17cc87e0529ac71d909a946afb47a274 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.566 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.048 seconds
Domain Reload Profiling: 1614ms
	BeginReloadAssembly (183ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (20ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (62ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (307ms)
		LoadAssemblies (338ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (41ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (20ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1048ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (525ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (357ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 5.13 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5956 Unused Serialized files (Serialized files now loaded: 0)
Unloading 30 unused Assets / (319.6 KB). Loaded Objects now: 6741.
Memory consumption went from 205.9 MB to 205.5 MB.
Total: 4.406000 ms (FindLiveObjects: 0.370400 ms CreateObjectMapping: 0.302900 ms MarkObjects: 3.642000 ms  DeleteObjects: 0.090100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SkewTextExample.cs: c142d02d70a129e0533975f7c2f96956 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/TMP_TextEventHandler.cs: f5cd9d5a43bfefc8f4fec80e6de896a4 -> 
  custom:scripting/monoscript/fileName/TextMeshProFloatingText.cs: 92608fa4a6315cd0786d82205e77b697 -> 
  custom:scripting/monoscript/fileName/VertexZoom.cs: 650ab1e81d40126e3edcf737cce646e4 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/VertexShakeA.cs: 63ec564c588058369499638dd899e05b -> 
  custom:scripting/monoscript/fileName/TMP_ExampleScript_01.cs: a9d4be556104ff54e7d240529b3e01dc -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/TMP_TextEventCheck.cs: 13505d917eab5868c435e0c2986dadfa -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_B.cs: ba2be21a7486d37b1011342977a2e49d -> 
  custom:scripting/monoscript/fileName/Benchmark01.cs: 2d3e2cd961730ab95f139a49a673bace -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/Benchmark02.cs: 5640963252ce70910e726415f869ced6 -> 
  custom:scripting/monoscript/fileName/DropdownSample.cs: 0a92251794e5f10400149f96c3c59e5d -> 
  custom:scripting/monoscript/fileName/WarpTextExample.cs: 2663459c266363641981c7d17b698d7f -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/StorageSerializer.cs: bd2e1f85a8c0c08db143374b1c4b7bc5 -> 
  custom:scripting/monoscript/fileName/ChatController.cs: 876d5ccae6e345dd5d9450eb0a748f16 -> 
  custom:scripting/monoscript/fileName/TextMeshSpawner.cs: 76f28e2ddb031571e6ec62b018f65fa8 -> 
  custom:scripting/monoscript/fileName/TeleType.cs: 31fa97821c4c70f157ec81d507aa6b62 -> 
  custom:scripting/monoscript/fileName/Benchmark04.cs: 32220b148620e52aa81c13a114862bf4 -> 
  custom:scripting/monoscript/fileName/Storage.cs: 0d55ff6f69c85da5c119baec6b4da9c9 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/TMP_PhoneNumberValidator.cs: 7c3049f481e464c6a9a15dee56c3126f -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/EnvMapAnimator.cs: 1b4f61c17e4b7283c3ac351aacff9d26 -> 
  custom:scripting/monoscript/fileName/ShaderPropAnimator.cs: 55ce425812446482e623ff2695c94cb2 -> 
  custom:scripting/monoscript/fileName/SimpleScript.cs: 454caab0c6c050f29b39d82e1d66d0fa -> 
  custom:scripting/monoscript/fileName/ObjectSpin.cs: 2b96bb2079dd7fa5108d981627a28e1e -> 
  custom:scripting/monoscript/fileName/CameraController.cs: 1daac0040b3fce2177f6ca4f90eb8c5b -> 90f26c7c63924735c23c826b8a781463
  custom:scripting/monoscript/fileName/TMP_TextInfoDebugTool.cs: 3f1709cfb2cc628169113a35b5e9be3f -> 
  custom:scripting/monoscript/fileName/StorageExample.cs: fbf56fbbe4a92ff023ad02de60c4da0e -> 
  custom:scripting/monoscript/fileName/StorageEncryption.cs: 1665512cf453609b5d2b62e0a4819a91 -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_A.cs: 347309a09d56e367b188a6978f75b424 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/StorageSettings.cs: 35ec0a7219481e610f2bc52f370f6422 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/VertexJitter.cs: d27eb5498e48714a66aece98da065133 -> 
  custom:scripting/monoscript/fileName/TextConsoleSimulator.cs: 33fc311abb20b5b0ed8f00e4d86b574e -> 
  custom:scripting/monoscript/fileName/VertexColorCycler.cs: 5f984ca91cf3609293aecc6bc5641572 -> 
  custom:scripting/monoscript/fileName/TMP_FrameRateCounter.cs: 46f655df8cb483ac5958307137b1585a -> 
  custom:scripting/monoscript/fileName/VertexShakeB.cs: a6f78c4fcda1a29a1a1c1baefedba9e7 -> 
  custom:scripting/monoscript/fileName/TMP_UiFrameRateCounter.cs: b01021aea19247f14a98ab338907956a -> 
  custom:scripting/monoscript/fileName/TMPro_InstructionOverlay.cs: b626d2270e6ebd5c4311585c18b58d8d -> 
  custom:scripting/monoscript/fileName/StorageCache.cs: 73093796b6294aaac7481c89ba959971 -> 
  custom:scripting/monoscript/fileName/TMP_DigitValidator.cs: ac02af96215ae3951a3cce9e1a8c6085 -> 
  custom:scripting/monoscript/fileName/UnityJsonConverters.cs: 801e98db15bb29b0fbdc94637601d63e -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/Benchmark01_UGUI.cs: 6148cfe2627b7708298f301788144139 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 18d849333c205e92cd3e4fbcf0988960 -> 66c59ad6cee36014c710f4c18c6650dc
  custom:scripting/monoscript/fileName/Benchmark03.cs: 17cc87e0529ac71d909a946afb47a274 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.628 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.087 seconds
Domain Reload Profiling: 1715ms
	BeginReloadAssembly (184ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (69ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (368ms)
		LoadAssemblies (396ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (39ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (18ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1087ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (500ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (101ms)
			ProcessInitializeOnLoadAttributes (331ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 7.41 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5956 Unused Serialized files (Serialized files now loaded: 0)
Unloading 30 unused Assets / (319.6 KB). Loaded Objects now: 6747.
Memory consumption went from 205.9 MB to 205.6 MB.
Total: 4.181500 ms (FindLiveObjects: 0.390300 ms CreateObjectMapping: 0.269900 ms MarkObjects: 3.445400 ms  DeleteObjects: 0.075000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SkewTextExample.cs: c142d02d70a129e0533975f7c2f96956 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/TMP_TextEventHandler.cs: f5cd9d5a43bfefc8f4fec80e6de896a4 -> 
  custom:scripting/monoscript/fileName/TextMeshProFloatingText.cs: 92608fa4a6315cd0786d82205e77b697 -> 
  custom:scripting/monoscript/fileName/VertexZoom.cs: 650ab1e81d40126e3edcf737cce646e4 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/VertexShakeA.cs: 63ec564c588058369499638dd899e05b -> 
  custom:scripting/monoscript/fileName/TMP_ExampleScript_01.cs: a9d4be556104ff54e7d240529b3e01dc -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/TMP_TextEventCheck.cs: 13505d917eab5868c435e0c2986dadfa -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_B.cs: ba2be21a7486d37b1011342977a2e49d -> 
  custom:scripting/monoscript/fileName/Benchmark01.cs: 2d3e2cd961730ab95f139a49a673bace -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/Benchmark02.cs: 5640963252ce70910e726415f869ced6 -> 
  custom:scripting/monoscript/fileName/DropdownSample.cs: 0a92251794e5f10400149f96c3c59e5d -> 
  custom:scripting/monoscript/fileName/WarpTextExample.cs: 2663459c266363641981c7d17b698d7f -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/StorageSerializer.cs: bd2e1f85a8c0c08db143374b1c4b7bc5 -> 
  custom:scripting/monoscript/fileName/ChatController.cs: 876d5ccae6e345dd5d9450eb0a748f16 -> 
  custom:scripting/monoscript/fileName/TextMeshSpawner.cs: 76f28e2ddb031571e6ec62b018f65fa8 -> 
  custom:scripting/monoscript/fileName/TeleType.cs: 31fa97821c4c70f157ec81d507aa6b62 -> 
  custom:scripting/monoscript/fileName/Benchmark04.cs: 32220b148620e52aa81c13a114862bf4 -> 
  custom:scripting/monoscript/fileName/Storage.cs: 0d55ff6f69c85da5c119baec6b4da9c9 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/TMP_PhoneNumberValidator.cs: 7c3049f481e464c6a9a15dee56c3126f -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/EnvMapAnimator.cs: 1b4f61c17e4b7283c3ac351aacff9d26 -> 
  custom:scripting/monoscript/fileName/ShaderPropAnimator.cs: 55ce425812446482e623ff2695c94cb2 -> 
  custom:scripting/monoscript/fileName/SimpleScript.cs: 454caab0c6c050f29b39d82e1d66d0fa -> 
  custom:scripting/monoscript/fileName/ObjectSpin.cs: 2b96bb2079dd7fa5108d981627a28e1e -> 
  custom:scripting/monoscript/fileName/CameraController.cs: 1daac0040b3fce2177f6ca4f90eb8c5b -> 90f26c7c63924735c23c826b8a781463
  custom:scripting/monoscript/fileName/TMP_TextInfoDebugTool.cs: 3f1709cfb2cc628169113a35b5e9be3f -> 
  custom:scripting/monoscript/fileName/StorageExample.cs: fbf56fbbe4a92ff023ad02de60c4da0e -> 
  custom:scripting/monoscript/fileName/StorageEncryption.cs: 1665512cf453609b5d2b62e0a4819a91 -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_A.cs: 347309a09d56e367b188a6978f75b424 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/StorageSettings.cs: 35ec0a7219481e610f2bc52f370f6422 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/VertexJitter.cs: d27eb5498e48714a66aece98da065133 -> 
  custom:scripting/monoscript/fileName/TextConsoleSimulator.cs: 33fc311abb20b5b0ed8f00e4d86b574e -> 
  custom:scripting/monoscript/fileName/VertexColorCycler.cs: 5f984ca91cf3609293aecc6bc5641572 -> 
  custom:scripting/monoscript/fileName/TMP_FrameRateCounter.cs: 46f655df8cb483ac5958307137b1585a -> 
  custom:scripting/monoscript/fileName/VertexShakeB.cs: a6f78c4fcda1a29a1a1c1baefedba9e7 -> 
  custom:scripting/monoscript/fileName/TMP_UiFrameRateCounter.cs: b01021aea19247f14a98ab338907956a -> 
  custom:scripting/monoscript/fileName/TMPro_InstructionOverlay.cs: b626d2270e6ebd5c4311585c18b58d8d -> 
  custom:scripting/monoscript/fileName/StorageCache.cs: 73093796b6294aaac7481c89ba959971 -> 
  custom:scripting/monoscript/fileName/TMP_DigitValidator.cs: ac02af96215ae3951a3cce9e1a8c6085 -> 
  custom:scripting/monoscript/fileName/UnityJsonConverters.cs: 801e98db15bb29b0fbdc94637601d63e -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/Benchmark01_UGUI.cs: 6148cfe2627b7708298f301788144139 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 9e3dca2a6f7493b2f20b95ab1f072c71 -> 66c59ad6cee36014c710f4c18c6650dc
  custom:scripting/monoscript/fileName/Benchmark03.cs: 17cc87e0529ac71d909a946afb47a274 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.691 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.030 seconds
Domain Reload Profiling: 1721ms
	BeginReloadAssembly (229ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (22ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (84ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (55ms)
	LoadAllAssembliesAndSetupDomain (368ms)
		LoadAssemblies (414ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (42ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (19ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1030ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (508ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (100ms)
			ProcessInitializeOnLoadAttributes (339ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 6.93 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5956 Unused Serialized files (Serialized files now loaded: 0)
Unloading 30 unused Assets / (319.6 KB). Loaded Objects now: 6753.
Memory consumption went from 205.9 MB to 205.6 MB.
Total: 3.852700 ms (FindLiveObjects: 0.345500 ms CreateObjectMapping: 0.237100 ms MarkObjects: 3.183700 ms  DeleteObjects: 0.085800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SkewTextExample.cs: c142d02d70a129e0533975f7c2f96956 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/TMP_TextEventHandler.cs: f5cd9d5a43bfefc8f4fec80e6de896a4 -> 
  custom:scripting/monoscript/fileName/TextMeshProFloatingText.cs: 92608fa4a6315cd0786d82205e77b697 -> 
  custom:scripting/monoscript/fileName/VertexZoom.cs: 650ab1e81d40126e3edcf737cce646e4 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/VertexShakeA.cs: 63ec564c588058369499638dd899e05b -> 
  custom:scripting/monoscript/fileName/TMP_ExampleScript_01.cs: a9d4be556104ff54e7d240529b3e01dc -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/TMP_TextEventCheck.cs: 13505d917eab5868c435e0c2986dadfa -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_B.cs: ba2be21a7486d37b1011342977a2e49d -> 
  custom:scripting/monoscript/fileName/Benchmark01.cs: 2d3e2cd961730ab95f139a49a673bace -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/Benchmark02.cs: 5640963252ce70910e726415f869ced6 -> 
  custom:scripting/monoscript/fileName/DropdownSample.cs: 0a92251794e5f10400149f96c3c59e5d -> 
  custom:scripting/monoscript/fileName/WarpTextExample.cs: 2663459c266363641981c7d17b698d7f -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/StorageSerializer.cs: bd2e1f85a8c0c08db143374b1c4b7bc5 -> 
  custom:scripting/monoscript/fileName/ChatController.cs: 876d5ccae6e345dd5d9450eb0a748f16 -> 
  custom:scripting/monoscript/fileName/TextMeshSpawner.cs: 76f28e2ddb031571e6ec62b018f65fa8 -> 
  custom:scripting/monoscript/fileName/TeleType.cs: 31fa97821c4c70f157ec81d507aa6b62 -> 
  custom:scripting/monoscript/fileName/Benchmark04.cs: 32220b148620e52aa81c13a114862bf4 -> 
  custom:scripting/monoscript/fileName/Storage.cs: 0d55ff6f69c85da5c119baec6b4da9c9 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/TMP_PhoneNumberValidator.cs: 7c3049f481e464c6a9a15dee56c3126f -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/EnvMapAnimator.cs: 1b4f61c17e4b7283c3ac351aacff9d26 -> 
  custom:scripting/monoscript/fileName/ShaderPropAnimator.cs: 55ce425812446482e623ff2695c94cb2 -> 
  custom:scripting/monoscript/fileName/SimpleScript.cs: 454caab0c6c050f29b39d82e1d66d0fa -> 
  custom:scripting/monoscript/fileName/ObjectSpin.cs: 2b96bb2079dd7fa5108d981627a28e1e -> 
  custom:scripting/monoscript/fileName/CameraController.cs: 1daac0040b3fce2177f6ca4f90eb8c5b -> 90f26c7c63924735c23c826b8a781463
  custom:scripting/monoscript/fileName/TMP_TextInfoDebugTool.cs: 3f1709cfb2cc628169113a35b5e9be3f -> 
  custom:scripting/monoscript/fileName/StorageExample.cs: fbf56fbbe4a92ff023ad02de60c4da0e -> 
  custom:scripting/monoscript/fileName/StorageEncryption.cs: 1665512cf453609b5d2b62e0a4819a91 -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_A.cs: 347309a09d56e367b188a6978f75b424 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/StorageSettings.cs: 35ec0a7219481e610f2bc52f370f6422 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/VertexJitter.cs: d27eb5498e48714a66aece98da065133 -> 
  custom:scripting/monoscript/fileName/TextConsoleSimulator.cs: 33fc311abb20b5b0ed8f00e4d86b574e -> 
  custom:scripting/monoscript/fileName/VertexColorCycler.cs: 5f984ca91cf3609293aecc6bc5641572 -> 
  custom:scripting/monoscript/fileName/TMP_FrameRateCounter.cs: 46f655df8cb483ac5958307137b1585a -> 
  custom:scripting/monoscript/fileName/VertexShakeB.cs: a6f78c4fcda1a29a1a1c1baefedba9e7 -> 
  custom:scripting/monoscript/fileName/TMP_UiFrameRateCounter.cs: b01021aea19247f14a98ab338907956a -> 
  custom:scripting/monoscript/fileName/TMPro_InstructionOverlay.cs: b626d2270e6ebd5c4311585c18b58d8d -> 
  custom:scripting/monoscript/fileName/StorageCache.cs: 73093796b6294aaac7481c89ba959971 -> 
  custom:scripting/monoscript/fileName/TMP_DigitValidator.cs: ac02af96215ae3951a3cce9e1a8c6085 -> 
  custom:scripting/monoscript/fileName/UnityJsonConverters.cs: 801e98db15bb29b0fbdc94637601d63e -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/Benchmark01_UGUI.cs: 6148cfe2627b7708298f301788144139 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 9e3dca2a6f7493b2f20b95ab1f072c71 -> 66c59ad6cee36014c710f4c18c6650dc
  custom:scripting/monoscript/fileName/Benchmark03.cs: 17cc87e0529ac71d909a946afb47a274 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.828 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Error updating workspace file: Sharing violation on path H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:71)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 71)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.430 seconds
Domain Reload Profiling: 2258ms
	BeginReloadAssembly (229ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (19ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (85ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (56ms)
	LoadAllAssembliesAndSetupDomain (491ms)
		LoadAssemblies (520ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (56ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (24ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1431ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (725ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (8ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (160ms)
			ProcessInitializeOnLoadAttributes (468ms)
			ProcessInitializeOnLoadMethodAttributes (81ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 7.88 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5958 Unused Serialized files (Serialized files now loaded: 0)
Unloading 30 unused Assets / (319.7 KB). Loaded Objects now: 6761.
Memory consumption went from 205.9 MB to 205.6 MB.
Total: 5.171400 ms (FindLiveObjects: 0.652300 ms CreateObjectMapping: 0.196800 ms MarkObjects: 4.211400 ms  DeleteObjects: 0.110100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SkewTextExample.cs: c142d02d70a129e0533975f7c2f96956 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/TMP_TextEventHandler.cs: f5cd9d5a43bfefc8f4fec80e6de896a4 -> 
  custom:scripting/monoscript/fileName/TextMeshProFloatingText.cs: 92608fa4a6315cd0786d82205e77b697 -> 
  custom:scripting/monoscript/fileName/VertexZoom.cs: 650ab1e81d40126e3edcf737cce646e4 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/VertexShakeA.cs: 63ec564c588058369499638dd899e05b -> 
  custom:scripting/monoscript/fileName/TMP_ExampleScript_01.cs: a9d4be556104ff54e7d240529b3e01dc -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/TMP_TextEventCheck.cs: 13505d917eab5868c435e0c2986dadfa -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_B.cs: ba2be21a7486d37b1011342977a2e49d -> 
  custom:scripting/monoscript/fileName/Benchmark01.cs: 2d3e2cd961730ab95f139a49a673bace -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/Benchmark02.cs: 5640963252ce70910e726415f869ced6 -> 
  custom:scripting/monoscript/fileName/DropdownSample.cs: 0a92251794e5f10400149f96c3c59e5d -> 
  custom:scripting/monoscript/fileName/WarpTextExample.cs: 2663459c266363641981c7d17b698d7f -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/StorageSerializer.cs: bd2e1f85a8c0c08db143374b1c4b7bc5 -> 
  custom:scripting/monoscript/fileName/ChatController.cs: 876d5ccae6e345dd5d9450eb0a748f16 -> 
  custom:scripting/monoscript/fileName/TextMeshSpawner.cs: 76f28e2ddb031571e6ec62b018f65fa8 -> 
  custom:scripting/monoscript/fileName/TeleType.cs: 31fa97821c4c70f157ec81d507aa6b62 -> 
  custom:scripting/monoscript/fileName/Benchmark04.cs: 32220b148620e52aa81c13a114862bf4 -> 
  custom:scripting/monoscript/fileName/Storage.cs: 0d55ff6f69c85da5c119baec6b4da9c9 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/TMP_PhoneNumberValidator.cs: 7c3049f481e464c6a9a15dee56c3126f -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/EnvMapAnimator.cs: 1b4f61c17e4b7283c3ac351aacff9d26 -> 
  custom:scripting/monoscript/fileName/ShaderPropAnimator.cs: 55ce425812446482e623ff2695c94cb2 -> 
  custom:scripting/monoscript/fileName/SimpleScript.cs: 454caab0c6c050f29b39d82e1d66d0fa -> 
  custom:scripting/monoscript/fileName/ObjectSpin.cs: 2b96bb2079dd7fa5108d981627a28e1e -> 
  custom:scripting/monoscript/fileName/CameraController.cs: 1daac0040b3fce2177f6ca4f90eb8c5b -> 90f26c7c63924735c23c826b8a781463
  custom:scripting/monoscript/fileName/TMP_TextInfoDebugTool.cs: 3f1709cfb2cc628169113a35b5e9be3f -> 
  custom:scripting/monoscript/fileName/StorageExample.cs: fbf56fbbe4a92ff023ad02de60c4da0e -> 
  custom:scripting/monoscript/fileName/StorageEncryption.cs: 1665512cf453609b5d2b62e0a4819a91 -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_A.cs: 347309a09d56e367b188a6978f75b424 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/StorageSettings.cs: 35ec0a7219481e610f2bc52f370f6422 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/VertexJitter.cs: d27eb5498e48714a66aece98da065133 -> 
  custom:scripting/monoscript/fileName/StorageTest.cs: 1c552e25ef1ff26b31190a379e271b83 -> 
  custom:scripting/monoscript/fileName/TextConsoleSimulator.cs: 33fc311abb20b5b0ed8f00e4d86b574e -> 
  custom:scripting/monoscript/fileName/VertexColorCycler.cs: 5f984ca91cf3609293aecc6bc5641572 -> 
  custom:scripting/monoscript/fileName/TMP_FrameRateCounter.cs: 46f655df8cb483ac5958307137b1585a -> 
  custom:scripting/monoscript/fileName/VertexShakeB.cs: a6f78c4fcda1a29a1a1c1baefedba9e7 -> 
  custom:scripting/monoscript/fileName/TMP_UiFrameRateCounter.cs: b01021aea19247f14a98ab338907956a -> 
  custom:scripting/monoscript/fileName/TMPro_InstructionOverlay.cs: b626d2270e6ebd5c4311585c18b58d8d -> 
  custom:scripting/monoscript/fileName/StorageCache.cs: 73093796b6294aaac7481c89ba959971 -> 
  custom:scripting/monoscript/fileName/TMP_DigitValidator.cs: ac02af96215ae3951a3cce9e1a8c6085 -> 
  custom:scripting/monoscript/fileName/UnityJsonConverters.cs: 801e98db15bb29b0fbdc94637601d63e -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/Benchmark01_UGUI.cs: 6148cfe2627b7708298f301788144139 -> 
  custom:scripting/monoscript/fileName/StorageMainThreadProcessor.cs: b90fbfcc1e7d025cb6b92e192e3f5bf7 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: b069f32919494ab57b416b24146a2f83 -> 66c59ad6cee36014c710f4c18c6650dc
  custom:scripting/monoscript/fileName/Benchmark03.cs: 17cc87e0529ac71d909a946afb47a274 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.633 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.144 seconds
Domain Reload Profiling: 1777ms
	BeginReloadAssembly (178ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (65ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (372ms)
		LoadAssemblies (418ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (21ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1144ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (562ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (375ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 4.86 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5958 Unused Serialized files (Serialized files now loaded: 0)
Unloading 30 unused Assets / (319.7 KB). Loaded Objects now: 6767.
Memory consumption went from 205.9 MB to 205.6 MB.
Total: 3.882800 ms (FindLiveObjects: 0.349600 ms CreateObjectMapping: 0.306700 ms MarkObjects: 3.142900 ms  DeleteObjects: 0.082800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SkewTextExample.cs: c142d02d70a129e0533975f7c2f96956 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/TMP_TextEventHandler.cs: f5cd9d5a43bfefc8f4fec80e6de896a4 -> 
  custom:scripting/monoscript/fileName/TextMeshProFloatingText.cs: 92608fa4a6315cd0786d82205e77b697 -> 
  custom:scripting/monoscript/fileName/VertexZoom.cs: 650ab1e81d40126e3edcf737cce646e4 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/VertexShakeA.cs: 63ec564c588058369499638dd899e05b -> 
  custom:scripting/monoscript/fileName/TMP_ExampleScript_01.cs: a9d4be556104ff54e7d240529b3e01dc -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/TMP_TextEventCheck.cs: 13505d917eab5868c435e0c2986dadfa -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_B.cs: ba2be21a7486d37b1011342977a2e49d -> 
  custom:scripting/monoscript/fileName/Benchmark01.cs: 2d3e2cd961730ab95f139a49a673bace -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/Benchmark02.cs: 5640963252ce70910e726415f869ced6 -> 
  custom:scripting/monoscript/fileName/DropdownSample.cs: 0a92251794e5f10400149f96c3c59e5d -> 
  custom:scripting/monoscript/fileName/WarpTextExample.cs: 2663459c266363641981c7d17b698d7f -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/StorageSerializer.cs: bd2e1f85a8c0c08db143374b1c4b7bc5 -> 
  custom:scripting/monoscript/fileName/ChatController.cs: 876d5ccae6e345dd5d9450eb0a748f16 -> 
  custom:scripting/monoscript/fileName/TextMeshSpawner.cs: 76f28e2ddb031571e6ec62b018f65fa8 -> 
  custom:scripting/monoscript/fileName/TeleType.cs: 31fa97821c4c70f157ec81d507aa6b62 -> 
  custom:scripting/monoscript/fileName/Benchmark04.cs: 32220b148620e52aa81c13a114862bf4 -> 
  custom:scripting/monoscript/fileName/Storage.cs: 0d55ff6f69c85da5c119baec6b4da9c9 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/TMP_PhoneNumberValidator.cs: 7c3049f481e464c6a9a15dee56c3126f -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/EnvMapAnimator.cs: 1b4f61c17e4b7283c3ac351aacff9d26 -> 
  custom:scripting/monoscript/fileName/ShaderPropAnimator.cs: 55ce425812446482e623ff2695c94cb2 -> 
  custom:scripting/monoscript/fileName/SimpleScript.cs: 454caab0c6c050f29b39d82e1d66d0fa -> 
  custom:scripting/monoscript/fileName/ObjectSpin.cs: 2b96bb2079dd7fa5108d981627a28e1e -> 
  custom:scripting/monoscript/fileName/CameraController.cs: 1daac0040b3fce2177f6ca4f90eb8c5b -> 90f26c7c63924735c23c826b8a781463
  custom:scripting/monoscript/fileName/TMP_TextInfoDebugTool.cs: 3f1709cfb2cc628169113a35b5e9be3f -> 
  custom:scripting/monoscript/fileName/StorageExample.cs: fbf56fbbe4a92ff023ad02de60c4da0e -> 
  custom:scripting/monoscript/fileName/StorageEncryption.cs: 1665512cf453609b5d2b62e0a4819a91 -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_A.cs: 347309a09d56e367b188a6978f75b424 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/StorageSettings.cs: 35ec0a7219481e610f2bc52f370f6422 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/VertexJitter.cs: d27eb5498e48714a66aece98da065133 -> 
  custom:scripting/monoscript/fileName/StorageTest.cs: 1c552e25ef1ff26b31190a379e271b83 -> 
  custom:scripting/monoscript/fileName/TextConsoleSimulator.cs: 33fc311abb20b5b0ed8f00e4d86b574e -> 
  custom:scripting/monoscript/fileName/VertexColorCycler.cs: 5f984ca91cf3609293aecc6bc5641572 -> 
  custom:scripting/monoscript/fileName/TMP_FrameRateCounter.cs: 46f655df8cb483ac5958307137b1585a -> 
  custom:scripting/monoscript/fileName/VertexShakeB.cs: a6f78c4fcda1a29a1a1c1baefedba9e7 -> 
  custom:scripting/monoscript/fileName/TMP_UiFrameRateCounter.cs: b01021aea19247f14a98ab338907956a -> 
  custom:scripting/monoscript/fileName/TMPro_InstructionOverlay.cs: b626d2270e6ebd5c4311585c18b58d8d -> 
  custom:scripting/monoscript/fileName/StorageCache.cs: 73093796b6294aaac7481c89ba959971 -> 
  custom:scripting/monoscript/fileName/TMP_DigitValidator.cs: ac02af96215ae3951a3cce9e1a8c6085 -> 
  custom:scripting/monoscript/fileName/UnityJsonConverters.cs: 801e98db15bb29b0fbdc94637601d63e -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/Benchmark01_UGUI.cs: 6148cfe2627b7708298f301788144139 -> 
  custom:scripting/monoscript/fileName/StorageMainThreadProcessor.cs: b90fbfcc1e7d025cb6b92e192e3f5bf7 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: b069f32919494ab57b416b24146a2f83 -> 66c59ad6cee36014c710f4c18c6650dc
  custom:scripting/monoscript/fileName/Benchmark03.cs: 17cc87e0529ac71d909a946afb47a274 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.640 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.055 seconds
Domain Reload Profiling: 1694ms
	BeginReloadAssembly (178ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (65ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (387ms)
		LoadAssemblies (412ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (41ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (18ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1055ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (512ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (102ms)
			ProcessInitializeOnLoadAttributes (341ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 6.45 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5958 Unused Serialized files (Serialized files now loaded: 0)
Unloading 30 unused Assets / (319.6 KB). Loaded Objects now: 6773.
Memory consumption went from 206.0 MB to 205.6 MB.
Total: 3.864500 ms (FindLiveObjects: 0.344200 ms CreateObjectMapping: 0.254000 ms MarkObjects: 3.189100 ms  DeleteObjects: 0.076600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SkewTextExample.cs: c142d02d70a129e0533975f7c2f96956 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/TMP_TextEventHandler.cs: f5cd9d5a43bfefc8f4fec80e6de896a4 -> 
  custom:scripting/monoscript/fileName/TextMeshProFloatingText.cs: 92608fa4a6315cd0786d82205e77b697 -> 
  custom:scripting/monoscript/fileName/VertexZoom.cs: 650ab1e81d40126e3edcf737cce646e4 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/VertexShakeA.cs: 63ec564c588058369499638dd899e05b -> 
  custom:scripting/monoscript/fileName/TMP_ExampleScript_01.cs: a9d4be556104ff54e7d240529b3e01dc -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/TMP_TextEventCheck.cs: 13505d917eab5868c435e0c2986dadfa -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_B.cs: ba2be21a7486d37b1011342977a2e49d -> 
  custom:scripting/monoscript/fileName/Benchmark01.cs: 2d3e2cd961730ab95f139a49a673bace -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/Benchmark02.cs: 5640963252ce70910e726415f869ced6 -> 
  custom:scripting/monoscript/fileName/DropdownSample.cs: 0a92251794e5f10400149f96c3c59e5d -> 
  custom:scripting/monoscript/fileName/WarpTextExample.cs: 2663459c266363641981c7d17b698d7f -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/StorageSerializer.cs: bd2e1f85a8c0c08db143374b1c4b7bc5 -> 
  custom:scripting/monoscript/fileName/ChatController.cs: 876d5ccae6e345dd5d9450eb0a748f16 -> 
  custom:scripting/monoscript/fileName/TextMeshSpawner.cs: 76f28e2ddb031571e6ec62b018f65fa8 -> 
  custom:scripting/monoscript/fileName/TeleType.cs: 31fa97821c4c70f157ec81d507aa6b62 -> 
  custom:scripting/monoscript/fileName/Benchmark04.cs: 32220b148620e52aa81c13a114862bf4 -> 
  custom:scripting/monoscript/fileName/Storage.cs: 0d55ff6f69c85da5c119baec6b4da9c9 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/TMP_PhoneNumberValidator.cs: 7c3049f481e464c6a9a15dee56c3126f -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/EnvMapAnimator.cs: 1b4f61c17e4b7283c3ac351aacff9d26 -> 
  custom:scripting/monoscript/fileName/ShaderPropAnimator.cs: 55ce425812446482e623ff2695c94cb2 -> 
  custom:scripting/monoscript/fileName/SimpleScript.cs: 454caab0c6c050f29b39d82e1d66d0fa -> 
  custom:scripting/monoscript/fileName/ObjectSpin.cs: 2b96bb2079dd7fa5108d981627a28e1e -> 
  custom:scripting/monoscript/fileName/CameraController.cs: 1daac0040b3fce2177f6ca4f90eb8c5b -> 90f26c7c63924735c23c826b8a781463
  custom:scripting/monoscript/fileName/TMP_TextInfoDebugTool.cs: 3f1709cfb2cc628169113a35b5e9be3f -> 
  custom:scripting/monoscript/fileName/StorageExample.cs: fbf56fbbe4a92ff023ad02de60c4da0e -> 
  custom:scripting/monoscript/fileName/StorageEncryption.cs: 1665512cf453609b5d2b62e0a4819a91 -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_A.cs: 347309a09d56e367b188a6978f75b424 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/StorageSettings.cs: 35ec0a7219481e610f2bc52f370f6422 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/VertexJitter.cs: d27eb5498e48714a66aece98da065133 -> 
  custom:scripting/monoscript/fileName/StorageTest.cs: 1c552e25ef1ff26b31190a379e271b83 -> 
  custom:scripting/monoscript/fileName/TextConsoleSimulator.cs: 33fc311abb20b5b0ed8f00e4d86b574e -> 
  custom:scripting/monoscript/fileName/VertexColorCycler.cs: 5f984ca91cf3609293aecc6bc5641572 -> 
  custom:scripting/monoscript/fileName/TMP_FrameRateCounter.cs: 46f655df8cb483ac5958307137b1585a -> 
  custom:scripting/monoscript/fileName/VertexShakeB.cs: a6f78c4fcda1a29a1a1c1baefedba9e7 -> 
  custom:scripting/monoscript/fileName/TMP_UiFrameRateCounter.cs: b01021aea19247f14a98ab338907956a -> 
  custom:scripting/monoscript/fileName/TMPro_InstructionOverlay.cs: b626d2270e6ebd5c4311585c18b58d8d -> 
  custom:scripting/monoscript/fileName/StorageCache.cs: 73093796b6294aaac7481c89ba959971 -> 
  custom:scripting/monoscript/fileName/TMP_DigitValidator.cs: ac02af96215ae3951a3cce9e1a8c6085 -> 
  custom:scripting/monoscript/fileName/UnityJsonConverters.cs: 801e98db15bb29b0fbdc94637601d63e -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/Benchmark01_UGUI.cs: 6148cfe2627b7708298f301788144139 -> 
  custom:scripting/monoscript/fileName/StorageMainThreadProcessor.cs: b90fbfcc1e7d025cb6b92e192e3f5bf7 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: b069f32919494ab57b416b24146a2f83 -> 66c59ad6cee36014c710f4c18c6650dc
  custom:scripting/monoscript/fileName/Benchmark03.cs: 17cc87e0529ac71d909a946afb47a274 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.436 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.055 seconds
Domain Reload Profiling: 1491ms
	BeginReloadAssembly (120ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (246ms)
		LoadAssemblies (286ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (18ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1055ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (508ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (339ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 7.69 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5958 Unused Serialized files (Serialized files now loaded: 0)
Unloading 30 unused Assets / (320.7 KB). Loaded Objects now: 6779.
Memory consumption went from 206.0 MB to 205.7 MB.
Total: 3.658500 ms (FindLiveObjects: 0.350500 ms CreateObjectMapping: 0.308500 ms MarkObjects: 2.919600 ms  DeleteObjects: 0.079300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SkewTextExample.cs: c142d02d70a129e0533975f7c2f96956 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/TMP_TextEventHandler.cs: f5cd9d5a43bfefc8f4fec80e6de896a4 -> 
  custom:scripting/monoscript/fileName/TextMeshProFloatingText.cs: 92608fa4a6315cd0786d82205e77b697 -> 
  custom:scripting/monoscript/fileName/VertexZoom.cs: 650ab1e81d40126e3edcf737cce646e4 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/VertexShakeA.cs: 63ec564c588058369499638dd899e05b -> 
  custom:scripting/monoscript/fileName/TMP_ExampleScript_01.cs: a9d4be556104ff54e7d240529b3e01dc -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/TMP_TextEventCheck.cs: 13505d917eab5868c435e0c2986dadfa -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_B.cs: ba2be21a7486d37b1011342977a2e49d -> 
  custom:scripting/monoscript/fileName/Benchmark01.cs: 2d3e2cd961730ab95f139a49a673bace -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/Benchmark02.cs: 5640963252ce70910e726415f869ced6 -> 
  custom:scripting/monoscript/fileName/DropdownSample.cs: 0a92251794e5f10400149f96c3c59e5d -> 
  custom:scripting/monoscript/fileName/WarpTextExample.cs: 2663459c266363641981c7d17b698d7f -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/StorageSerializer.cs: bd2e1f85a8c0c08db143374b1c4b7bc5 -> 
  custom:scripting/monoscript/fileName/ChatController.cs: 876d5ccae6e345dd5d9450eb0a748f16 -> 
  custom:scripting/monoscript/fileName/TextMeshSpawner.cs: 76f28e2ddb031571e6ec62b018f65fa8 -> 
  custom:scripting/monoscript/fileName/TeleType.cs: 31fa97821c4c70f157ec81d507aa6b62 -> 
  custom:scripting/monoscript/fileName/Benchmark04.cs: 32220b148620e52aa81c13a114862bf4 -> 
  custom:scripting/monoscript/fileName/Storage.cs: 0d55ff6f69c85da5c119baec6b4da9c9 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/TMP_PhoneNumberValidator.cs: 7c3049f481e464c6a9a15dee56c3126f -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/EnvMapAnimator.cs: 1b4f61c17e4b7283c3ac351aacff9d26 -> 
  custom:scripting/monoscript/fileName/ShaderPropAnimator.cs: 55ce425812446482e623ff2695c94cb2 -> 
  custom:scripting/monoscript/fileName/SimpleScript.cs: 454caab0c6c050f29b39d82e1d66d0fa -> 
  custom:scripting/monoscript/fileName/ObjectSpin.cs: 2b96bb2079dd7fa5108d981627a28e1e -> 
  custom:scripting/monoscript/fileName/CameraController.cs: 1daac0040b3fce2177f6ca4f90eb8c5b -> 90f26c7c63924735c23c826b8a781463
  custom:scripting/monoscript/fileName/TMP_TextInfoDebugTool.cs: 3f1709cfb2cc628169113a35b5e9be3f -> 
  custom:scripting/monoscript/fileName/StorageExample.cs: fbf56fbbe4a92ff023ad02de60c4da0e -> 
  custom:scripting/monoscript/fileName/StorageEncryption.cs: 1665512cf453609b5d2b62e0a4819a91 -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_A.cs: 347309a09d56e367b188a6978f75b424 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/StorageSettings.cs: 35ec0a7219481e610f2bc52f370f6422 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/VertexJitter.cs: d27eb5498e48714a66aece98da065133 -> 
  custom:scripting/monoscript/fileName/StorageTest.cs: 1c552e25ef1ff26b31190a379e271b83 -> 
  custom:scripting/monoscript/fileName/TextConsoleSimulator.cs: 33fc311abb20b5b0ed8f00e4d86b574e -> 
  custom:scripting/monoscript/fileName/VertexColorCycler.cs: 5f984ca91cf3609293aecc6bc5641572 -> 
  custom:scripting/monoscript/fileName/TMP_FrameRateCounter.cs: 46f655df8cb483ac5958307137b1585a -> 
  custom:scripting/monoscript/fileName/VertexShakeB.cs: a6f78c4fcda1a29a1a1c1baefedba9e7 -> 
  custom:scripting/monoscript/fileName/TMP_UiFrameRateCounter.cs: b01021aea19247f14a98ab338907956a -> 
  custom:scripting/monoscript/fileName/TMPro_InstructionOverlay.cs: b626d2270e6ebd5c4311585c18b58d8d -> 
  custom:scripting/monoscript/fileName/StorageCache.cs: 73093796b6294aaac7481c89ba959971 -> 
  custom:scripting/monoscript/fileName/TMP_DigitValidator.cs: ac02af96215ae3951a3cce9e1a8c6085 -> 
  custom:scripting/monoscript/fileName/UnityJsonConverters.cs: 801e98db15bb29b0fbdc94637601d63e -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/Benchmark01_UGUI.cs: 6148cfe2627b7708298f301788144139 -> 
  custom:scripting/monoscript/fileName/StorageMainThreadProcessor.cs: b90fbfcc1e7d025cb6b92e192e3f5bf7 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: b069f32919494ab57b416b24146a2f83 -> 66c59ad6cee36014c710f4c18c6650dc
  custom:scripting/monoscript/fileName/Benchmark03.cs: 17cc87e0529ac71d909a946afb47a274 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.432 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.100 seconds
Domain Reload Profiling: 1531ms
	BeginReloadAssembly (123ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (241ms)
		LoadAssemblies (278ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (18ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1100ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (544ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (104ms)
			ProcessInitializeOnLoadAttributes (367ms)
			ProcessInitializeOnLoadMethodAttributes (63ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 4.35 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5958 Unused Serialized files (Serialized files now loaded: 0)
Unloading 30 unused Assets / (319.7 KB). Loaded Objects now: 6785.
Memory consumption went from 206.0 MB to 205.7 MB.
Total: 3.230400 ms (FindLiveObjects: 0.300000 ms CreateObjectMapping: 0.232100 ms MarkObjects: 2.626400 ms  DeleteObjects: 0.071500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SkewTextExample.cs: c142d02d70a129e0533975f7c2f96956 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/TMP_TextEventHandler.cs: f5cd9d5a43bfefc8f4fec80e6de896a4 -> 
  custom:scripting/monoscript/fileName/TextMeshProFloatingText.cs: 92608fa4a6315cd0786d82205e77b697 -> 
  custom:scripting/monoscript/fileName/VertexZoom.cs: 650ab1e81d40126e3edcf737cce646e4 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/VertexShakeA.cs: 63ec564c588058369499638dd899e05b -> 
  custom:scripting/monoscript/fileName/TMP_ExampleScript_01.cs: a9d4be556104ff54e7d240529b3e01dc -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/TMP_TextEventCheck.cs: 13505d917eab5868c435e0c2986dadfa -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_B.cs: ba2be21a7486d37b1011342977a2e49d -> 
  custom:scripting/monoscript/fileName/Benchmark01.cs: 2d3e2cd961730ab95f139a49a673bace -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/Benchmark02.cs: 5640963252ce70910e726415f869ced6 -> 
  custom:scripting/monoscript/fileName/DropdownSample.cs: 0a92251794e5f10400149f96c3c59e5d -> 
  custom:scripting/monoscript/fileName/WarpTextExample.cs: 2663459c266363641981c7d17b698d7f -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/StorageSerializer.cs: bd2e1f85a8c0c08db143374b1c4b7bc5 -> 
  custom:scripting/monoscript/fileName/ChatController.cs: 876d5ccae6e345dd5d9450eb0a748f16 -> 
  custom:scripting/monoscript/fileName/TextMeshSpawner.cs: 76f28e2ddb031571e6ec62b018f65fa8 -> 
  custom:scripting/monoscript/fileName/TeleType.cs: 31fa97821c4c70f157ec81d507aa6b62 -> 
  custom:scripting/monoscript/fileName/Benchmark04.cs: 32220b148620e52aa81c13a114862bf4 -> 
  custom:scripting/monoscript/fileName/Storage.cs: 0d55ff6f69c85da5c119baec6b4da9c9 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/TMP_PhoneNumberValidator.cs: 7c3049f481e464c6a9a15dee56c3126f -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/EnvMapAnimator.cs: 1b4f61c17e4b7283c3ac351aacff9d26 -> 
  custom:scripting/monoscript/fileName/ShaderPropAnimator.cs: 55ce425812446482e623ff2695c94cb2 -> 
  custom:scripting/monoscript/fileName/SimpleScript.cs: 454caab0c6c050f29b39d82e1d66d0fa -> 
  custom:scripting/monoscript/fileName/ObjectSpin.cs: 2b96bb2079dd7fa5108d981627a28e1e -> 
  custom:scripting/monoscript/fileName/CameraController.cs: 1daac0040b3fce2177f6ca4f90eb8c5b -> 90f26c7c63924735c23c826b8a781463
  custom:scripting/monoscript/fileName/TMP_TextInfoDebugTool.cs: 3f1709cfb2cc628169113a35b5e9be3f -> 
  custom:scripting/monoscript/fileName/StorageExample.cs: fbf56fbbe4a92ff023ad02de60c4da0e -> 
  custom:scripting/monoscript/fileName/StorageEncryption.cs: 1665512cf453609b5d2b62e0a4819a91 -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_A.cs: 347309a09d56e367b188a6978f75b424 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/StorageSettings.cs: 35ec0a7219481e610f2bc52f370f6422 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/VertexJitter.cs: d27eb5498e48714a66aece98da065133 -> 
  custom:scripting/monoscript/fileName/StorageTest.cs: 1c552e25ef1ff26b31190a379e271b83 -> 
  custom:scripting/monoscript/fileName/TextConsoleSimulator.cs: 33fc311abb20b5b0ed8f00e4d86b574e -> 
  custom:scripting/monoscript/fileName/VertexColorCycler.cs: 5f984ca91cf3609293aecc6bc5641572 -> 
  custom:scripting/monoscript/fileName/TMP_FrameRateCounter.cs: 46f655df8cb483ac5958307137b1585a -> 
  custom:scripting/monoscript/fileName/VertexShakeB.cs: a6f78c4fcda1a29a1a1c1baefedba9e7 -> 
  custom:scripting/monoscript/fileName/TMP_UiFrameRateCounter.cs: b01021aea19247f14a98ab338907956a -> 
  custom:scripting/monoscript/fileName/TMPro_InstructionOverlay.cs: b626d2270e6ebd5c4311585c18b58d8d -> 
  custom:scripting/monoscript/fileName/StorageCache.cs: 73093796b6294aaac7481c89ba959971 -> 
  custom:scripting/monoscript/fileName/TMP_DigitValidator.cs: ac02af96215ae3951a3cce9e1a8c6085 -> 
  custom:scripting/monoscript/fileName/UnityJsonConverters.cs: 801e98db15bb29b0fbdc94637601d63e -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/Benchmark01_UGUI.cs: 6148cfe2627b7708298f301788144139 -> 
  custom:scripting/monoscript/fileName/StorageMainThreadProcessor.cs: b90fbfcc1e7d025cb6b92e192e3f5bf7 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: b069f32919494ab57b416b24146a2f83 -> 66c59ad6cee36014c710f4c18c6650dc
  custom:scripting/monoscript/fileName/Benchmark03.cs: 17cc87e0529ac71d909a946afb47a274 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.436 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] WebSocket server started on port 8090
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.McpLogger:LogInfo (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:21)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:102)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 102)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.026 seconds
Domain Reload Profiling: 1461ms
	BeginReloadAssembly (124ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (243ms)
		LoadAssemblies (281ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (19ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1026ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (490ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (100ms)
			ProcessInitializeOnLoadAttributes (317ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 7.55 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5958 Unused Serialized files (Serialized files now loaded: 0)
Unloading 30 unused Assets / (319.2 KB). Loaded Objects now: 6791.
Memory consumption went from 206.0 MB to 205.7 MB.
Total: 3.552000 ms (FindLiveObjects: 0.338000 ms CreateObjectMapping: 0.305300 ms MarkObjects: 2.827000 ms  DeleteObjects: 0.080800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SkewTextExample.cs: c142d02d70a129e0533975f7c2f96956 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/TMP_TextEventHandler.cs: f5cd9d5a43bfefc8f4fec80e6de896a4 -> 
  custom:scripting/monoscript/fileName/TextMeshProFloatingText.cs: 92608fa4a6315cd0786d82205e77b697 -> 
  custom:scripting/monoscript/fileName/VertexZoom.cs: 650ab1e81d40126e3edcf737cce646e4 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/VertexShakeA.cs: 63ec564c588058369499638dd899e05b -> 
  custom:scripting/monoscript/fileName/TMP_ExampleScript_01.cs: a9d4be556104ff54e7d240529b3e01dc -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/TMP_TextEventCheck.cs: 13505d917eab5868c435e0c2986dadfa -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_B.cs: ba2be21a7486d37b1011342977a2e49d -> 
  custom:scripting/monoscript/fileName/Benchmark01.cs: 2d3e2cd961730ab95f139a49a673bace -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/Benchmark02.cs: 5640963252ce70910e726415f869ced6 -> 
  custom:scripting/monoscript/fileName/DropdownSample.cs: 0a92251794e5f10400149f96c3c59e5d -> 
  custom:scripting/monoscript/fileName/WarpTextExample.cs: 2663459c266363641981c7d17b698d7f -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/StorageSerializer.cs: bd2e1f85a8c0c08db143374b1c4b7bc5 -> 
  custom:scripting/monoscript/fileName/ChatController.cs: 876d5ccae6e345dd5d9450eb0a748f16 -> 
  custom:scripting/monoscript/fileName/TextMeshSpawner.cs: 76f28e2ddb031571e6ec62b018f65fa8 -> 
  custom:scripting/monoscript/fileName/TeleType.cs: 31fa97821c4c70f157ec81d507aa6b62 -> 
  custom:scripting/monoscript/fileName/Benchmark04.cs: 32220b148620e52aa81c13a114862bf4 -> 
  custom:scripting/monoscript/fileName/Storage.cs: 0d55ff6f69c85da5c119baec6b4da9c9 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/TMP_PhoneNumberValidator.cs: 7c3049f481e464c6a9a15dee56c3126f -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/EnvMapAnimator.cs: 1b4f61c17e4b7283c3ac351aacff9d26 -> 
  custom:scripting/monoscript/fileName/ShaderPropAnimator.cs: 55ce425812446482e623ff2695c94cb2 -> 
  custom:scripting/monoscript/fileName/SimpleScript.cs: 454caab0c6c050f29b39d82e1d66d0fa -> 
  custom:scripting/monoscript/fileName/ObjectSpin.cs: 2b96bb2079dd7fa5108d981627a28e1e -> 
  custom:scripting/monoscript/fileName/CameraController.cs: 1daac0040b3fce2177f6ca4f90eb8c5b -> 90f26c7c63924735c23c826b8a781463
  custom:scripting/monoscript/fileName/TMP_TextInfoDebugTool.cs: 3f1709cfb2cc628169113a35b5e9be3f -> 
  custom:scripting/monoscript/fileName/StorageExample.cs: fbf56fbbe4a92ff023ad02de60c4da0e -> 
  custom:scripting/monoscript/fileName/StorageEncryption.cs: 1665512cf453609b5d2b62e0a4819a91 -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_A.cs: 347309a09d56e367b188a6978f75b424 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/StorageSettings.cs: 35ec0a7219481e610f2bc52f370f6422 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/VertexJitter.cs: d27eb5498e48714a66aece98da065133 -> 
  custom:scripting/monoscript/fileName/StorageTest.cs: 1c552e25ef1ff26b31190a379e271b83 -> 
  custom:scripting/monoscript/fileName/TextConsoleSimulator.cs: 33fc311abb20b5b0ed8f00e4d86b574e -> 
  custom:scripting/monoscript/fileName/VertexColorCycler.cs: 5f984ca91cf3609293aecc6bc5641572 -> 
  custom:scripting/monoscript/fileName/TMP_FrameRateCounter.cs: 46f655df8cb483ac5958307137b1585a -> 
  custom:scripting/monoscript/fileName/VertexShakeB.cs: a6f78c4fcda1a29a1a1c1baefedba9e7 -> 
  custom:scripting/monoscript/fileName/TMP_UiFrameRateCounter.cs: b01021aea19247f14a98ab338907956a -> 
  custom:scripting/monoscript/fileName/TMPro_InstructionOverlay.cs: b626d2270e6ebd5c4311585c18b58d8d -> 
  custom:scripting/monoscript/fileName/StorageCache.cs: 73093796b6294aaac7481c89ba959971 -> 
  custom:scripting/monoscript/fileName/TMP_DigitValidator.cs: ac02af96215ae3951a3cce9e1a8c6085 -> 
  custom:scripting/monoscript/fileName/UnityJsonConverters.cs: 801e98db15bb29b0fbdc94637601d63e -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/Benchmark01_UGUI.cs: 6148cfe2627b7708298f301788144139 -> 
  custom:scripting/monoscript/fileName/StorageMainThreadProcessor.cs: b90fbfcc1e7d025cb6b92e192e3f5bf7 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: b069f32919494ab57b416b24146a2f83 -> 66c59ad6cee36014c710f4c18c6650dc
  custom:scripting/monoscript/fileName/Benchmark03.cs: 17cc87e0529ac71d909a946afb47a274 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
2025/5/26 2:52:17|Fatal|WebSocketServer.receiveRequest|System.Threading.ThreadAbortException: Thread was being aborted.
                          at (wrapper managed-to-native) System.Net.Sockets.Socket.Accept_icall(intptr,int&,bool)
                          at System.Net.Sockets.Socket.Accept_internal (System.Net.Sockets.SafeSocketHandle safeHandle, System.Int32& error, System.Boolean blocking) [0x0000c] in <7ac9c2c0fb1643c4a015f148ef3a32d2>:0 
                          at System.Net.Sockets.Socket.Accept () [0x00008] in <7ac9c2c0fb1643c4a015f148ef3a32d2>:0 
                          at System.Net.Sockets.TcpListener.AcceptTcpClient () [0x0001e] in <7ac9c2c0fb1643c4a015f148ef3a32d2>:0 
                          at WebSocketSharp.Server.WebSocketServer.receiveRequest () [0x00012] in <38d3cef14c5a4fc9a92de0991034bc1a>:0 
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.466 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.999 seconds
Domain Reload Profiling: 1464ms
	BeginReloadAssembly (140ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (258ms)
		LoadAssemblies (278ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (37ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (17ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (999ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (491ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (97ms)
			ProcessInitializeOnLoadAttributes (328ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 7.91 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5958 Unused Serialized files (Serialized files now loaded: 0)
Unloading 30 unused Assets / (319.2 KB). Loaded Objects now: 6797.
Memory consumption went from 206.0 MB to 205.7 MB.
Total: 3.470700 ms (FindLiveObjects: 0.344300 ms CreateObjectMapping: 0.246700 ms MarkObjects: 2.783700 ms  DeleteObjects: 0.095200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SkewTextExample.cs: c142d02d70a129e0533975f7c2f96956 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/TMP_TextEventHandler.cs: f5cd9d5a43bfefc8f4fec80e6de896a4 -> 
  custom:scripting/monoscript/fileName/TextMeshProFloatingText.cs: 92608fa4a6315cd0786d82205e77b697 -> 
  custom:scripting/monoscript/fileName/VertexZoom.cs: 650ab1e81d40126e3edcf737cce646e4 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/VertexShakeA.cs: 63ec564c588058369499638dd899e05b -> 
  custom:scripting/monoscript/fileName/TMP_ExampleScript_01.cs: a9d4be556104ff54e7d240529b3e01dc -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/TMP_TextEventCheck.cs: 13505d917eab5868c435e0c2986dadfa -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_B.cs: ba2be21a7486d37b1011342977a2e49d -> 
  custom:scripting/monoscript/fileName/Benchmark01.cs: 2d3e2cd961730ab95f139a49a673bace -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/Benchmark02.cs: 5640963252ce70910e726415f869ced6 -> 
  custom:scripting/monoscript/fileName/DropdownSample.cs: 0a92251794e5f10400149f96c3c59e5d -> 
  custom:scripting/monoscript/fileName/WarpTextExample.cs: 2663459c266363641981c7d17b698d7f -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/StorageSerializer.cs: bd2e1f85a8c0c08db143374b1c4b7bc5 -> 
  custom:scripting/monoscript/fileName/ChatController.cs: 876d5ccae6e345dd5d9450eb0a748f16 -> 
  custom:scripting/monoscript/fileName/TextMeshSpawner.cs: 76f28e2ddb031571e6ec62b018f65fa8 -> 
  custom:scripting/monoscript/fileName/TeleType.cs: 31fa97821c4c70f157ec81d507aa6b62 -> 
  custom:scripting/monoscript/fileName/Benchmark04.cs: 32220b148620e52aa81c13a114862bf4 -> 
  custom:scripting/monoscript/fileName/Storage.cs: 0d55ff6f69c85da5c119baec6b4da9c9 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/TMP_PhoneNumberValidator.cs: 7c3049f481e464c6a9a15dee56c3126f -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/EnvMapAnimator.cs: 1b4f61c17e4b7283c3ac351aacff9d26 -> 
  custom:scripting/monoscript/fileName/ShaderPropAnimator.cs: 55ce425812446482e623ff2695c94cb2 -> 
  custom:scripting/monoscript/fileName/SimpleScript.cs: 454caab0c6c050f29b39d82e1d66d0fa -> 
  custom:scripting/monoscript/fileName/ObjectSpin.cs: 2b96bb2079dd7fa5108d981627a28e1e -> 
  custom:scripting/monoscript/fileName/CameraController.cs: 1daac0040b3fce2177f6ca4f90eb8c5b -> 90f26c7c63924735c23c826b8a781463
  custom:scripting/monoscript/fileName/TMP_TextInfoDebugTool.cs: 3f1709cfb2cc628169113a35b5e9be3f -> 
  custom:scripting/monoscript/fileName/StorageExample.cs: fbf56fbbe4a92ff023ad02de60c4da0e -> 
  custom:scripting/monoscript/fileName/StorageEncryption.cs: 1665512cf453609b5d2b62e0a4819a91 -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_A.cs: 347309a09d56e367b188a6978f75b424 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/StorageSettings.cs: 35ec0a7219481e610f2bc52f370f6422 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/VertexJitter.cs: d27eb5498e48714a66aece98da065133 -> 
  custom:scripting/monoscript/fileName/StorageTest.cs: 1c552e25ef1ff26b31190a379e271b83 -> 
  custom:scripting/monoscript/fileName/TextConsoleSimulator.cs: 33fc311abb20b5b0ed8f00e4d86b574e -> 
  custom:scripting/monoscript/fileName/VertexColorCycler.cs: 5f984ca91cf3609293aecc6bc5641572 -> 
  custom:scripting/monoscript/fileName/TMP_FrameRateCounter.cs: 46f655df8cb483ac5958307137b1585a -> 
  custom:scripting/monoscript/fileName/VertexShakeB.cs: a6f78c4fcda1a29a1a1c1baefedba9e7 -> 
  custom:scripting/monoscript/fileName/TMP_UiFrameRateCounter.cs: b01021aea19247f14a98ab338907956a -> 
  custom:scripting/monoscript/fileName/TMPro_InstructionOverlay.cs: b626d2270e6ebd5c4311585c18b58d8d -> 
  custom:scripting/monoscript/fileName/StorageCache.cs: 73093796b6294aaac7481c89ba959971 -> 
  custom:scripting/monoscript/fileName/TMP_DigitValidator.cs: ac02af96215ae3951a3cce9e1a8c6085 -> 
  custom:scripting/monoscript/fileName/UnityJsonConverters.cs: 801e98db15bb29b0fbdc94637601d63e -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/Benchmark01_UGUI.cs: 6148cfe2627b7708298f301788144139 -> 
  custom:scripting/monoscript/fileName/StorageMainThreadProcessor.cs: b90fbfcc1e7d025cb6b92e192e3f5bf7 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 25062a4c70fee9f4708966eeff35a9b5 -> 66c59ad6cee36014c710f4c18c6650dc
  custom:scripting/monoscript/fileName/Benchmark03.cs: 17cc87e0529ac71d909a946afb47a274 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.416 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.066 seconds
Domain Reload Profiling: 1482ms
	BeginReloadAssembly (119ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (230ms)
		LoadAssemblies (267ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (16ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1066ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (535ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (364ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 5.82 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5958 Unused Serialized files (Serialized files now loaded: 0)
Unloading 30 unused Assets / (319.6 KB). Loaded Objects now: 6803.
Memory consumption went from 206.0 MB to 205.7 MB.
Total: 3.115600 ms (FindLiveObjects: 0.335900 ms CreateObjectMapping: 0.220800 ms MarkObjects: 2.488400 ms  DeleteObjects: 0.070100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SkewTextExample.cs: c142d02d70a129e0533975f7c2f96956 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/TMP_TextEventHandler.cs: f5cd9d5a43bfefc8f4fec80e6de896a4 -> 
  custom:scripting/monoscript/fileName/TextMeshProFloatingText.cs: 92608fa4a6315cd0786d82205e77b697 -> 
  custom:scripting/monoscript/fileName/VertexZoom.cs: 650ab1e81d40126e3edcf737cce646e4 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/VertexShakeA.cs: 63ec564c588058369499638dd899e05b -> 
  custom:scripting/monoscript/fileName/TMP_ExampleScript_01.cs: a9d4be556104ff54e7d240529b3e01dc -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/TMP_TextEventCheck.cs: 13505d917eab5868c435e0c2986dadfa -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_B.cs: ba2be21a7486d37b1011342977a2e49d -> 
  custom:scripting/monoscript/fileName/Benchmark01.cs: 2d3e2cd961730ab95f139a49a673bace -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/Benchmark02.cs: 5640963252ce70910e726415f869ced6 -> 
  custom:scripting/monoscript/fileName/DropdownSample.cs: 0a92251794e5f10400149f96c3c59e5d -> 
  custom:scripting/monoscript/fileName/WarpTextExample.cs: 2663459c266363641981c7d17b698d7f -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/StorageSerializer.cs: bd2e1f85a8c0c08db143374b1c4b7bc5 -> 
  custom:scripting/monoscript/fileName/ChatController.cs: 876d5ccae6e345dd5d9450eb0a748f16 -> 
  custom:scripting/monoscript/fileName/TextMeshSpawner.cs: 76f28e2ddb031571e6ec62b018f65fa8 -> 
  custom:scripting/monoscript/fileName/TeleType.cs: 31fa97821c4c70f157ec81d507aa6b62 -> 
  custom:scripting/monoscript/fileName/Benchmark04.cs: 32220b148620e52aa81c13a114862bf4 -> 
  custom:scripting/monoscript/fileName/Storage.cs: 0d55ff6f69c85da5c119baec6b4da9c9 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/TMP_PhoneNumberValidator.cs: 7c3049f481e464c6a9a15dee56c3126f -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/EnvMapAnimator.cs: 1b4f61c17e4b7283c3ac351aacff9d26 -> 
  custom:scripting/monoscript/fileName/ShaderPropAnimator.cs: 55ce425812446482e623ff2695c94cb2 -> 
  custom:scripting/monoscript/fileName/SimpleScript.cs: 454caab0c6c050f29b39d82e1d66d0fa -> 
  custom:scripting/monoscript/fileName/ObjectSpin.cs: 2b96bb2079dd7fa5108d981627a28e1e -> 
  custom:scripting/monoscript/fileName/CameraController.cs: 1daac0040b3fce2177f6ca4f90eb8c5b -> 90f26c7c63924735c23c826b8a781463
  custom:scripting/monoscript/fileName/TMP_TextInfoDebugTool.cs: 3f1709cfb2cc628169113a35b5e9be3f -> 
  custom:scripting/monoscript/fileName/StorageExample.cs: fbf56fbbe4a92ff023ad02de60c4da0e -> 
  custom:scripting/monoscript/fileName/StorageEncryption.cs: 1665512cf453609b5d2b62e0a4819a91 -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_A.cs: 347309a09d56e367b188a6978f75b424 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/StorageSettings.cs: 35ec0a7219481e610f2bc52f370f6422 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/VertexJitter.cs: d27eb5498e48714a66aece98da065133 -> 
  custom:scripting/monoscript/fileName/StorageTest.cs: 1c552e25ef1ff26b31190a379e271b83 -> 
  custom:scripting/monoscript/fileName/TextConsoleSimulator.cs: 33fc311abb20b5b0ed8f00e4d86b574e -> 
  custom:scripting/monoscript/fileName/VertexColorCycler.cs: 5f984ca91cf3609293aecc6bc5641572 -> 
  custom:scripting/monoscript/fileName/TMP_FrameRateCounter.cs: 46f655df8cb483ac5958307137b1585a -> 
  custom:scripting/monoscript/fileName/VertexShakeB.cs: a6f78c4fcda1a29a1a1c1baefedba9e7 -> 
  custom:scripting/monoscript/fileName/TMP_UiFrameRateCounter.cs: b01021aea19247f14a98ab338907956a -> 
  custom:scripting/monoscript/fileName/TMPro_InstructionOverlay.cs: b626d2270e6ebd5c4311585c18b58d8d -> 
  custom:scripting/monoscript/fileName/StorageCache.cs: 73093796b6294aaac7481c89ba959971 -> 
  custom:scripting/monoscript/fileName/TMP_DigitValidator.cs: ac02af96215ae3951a3cce9e1a8c6085 -> 
  custom:scripting/monoscript/fileName/UnityJsonConverters.cs: 801e98db15bb29b0fbdc94637601d63e -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/Benchmark01_UGUI.cs: 6148cfe2627b7708298f301788144139 -> 
  custom:scripting/monoscript/fileName/StorageMainThreadProcessor.cs: b90fbfcc1e7d025cb6b92e192e3f5bf7 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 25062a4c70fee9f4708966eeff35a9b5 -> 66c59ad6cee36014c710f4c18c6650dc
  custom:scripting/monoscript/fileName/Benchmark03.cs: 17cc87e0529ac71d909a946afb47a274 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.656 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.153 seconds
Domain Reload Profiling: 1809ms
	BeginReloadAssembly (187ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (70ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (395ms)
		LoadAssemblies (418ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (45ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (19ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1153ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (568ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (374ms)
			ProcessInitializeOnLoadMethodAttributes (70ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 7.51 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5958 Unused Serialized files (Serialized files now loaded: 0)
Unloading 30 unused Assets / (319.7 KB). Loaded Objects now: 6809.
Memory consumption went from 206.0 MB to 205.7 MB.
Total: 4.172300 ms (FindLiveObjects: 0.397700 ms CreateObjectMapping: 0.352800 ms MarkObjects: 3.306700 ms  DeleteObjects: 0.114300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SkewTextExample.cs: c142d02d70a129e0533975f7c2f96956 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/TMP_TextEventHandler.cs: f5cd9d5a43bfefc8f4fec80e6de896a4 -> 
  custom:scripting/monoscript/fileName/TextMeshProFloatingText.cs: 92608fa4a6315cd0786d82205e77b697 -> 
  custom:scripting/monoscript/fileName/VertexZoom.cs: 650ab1e81d40126e3edcf737cce646e4 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/VertexShakeA.cs: 63ec564c588058369499638dd899e05b -> 
  custom:scripting/monoscript/fileName/TMP_ExampleScript_01.cs: a9d4be556104ff54e7d240529b3e01dc -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/TMP_TextEventCheck.cs: 13505d917eab5868c435e0c2986dadfa -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_B.cs: ba2be21a7486d37b1011342977a2e49d -> 
  custom:scripting/monoscript/fileName/Benchmark01.cs: 2d3e2cd961730ab95f139a49a673bace -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/Benchmark02.cs: 5640963252ce70910e726415f869ced6 -> 
  custom:scripting/monoscript/fileName/DropdownSample.cs: 0a92251794e5f10400149f96c3c59e5d -> 
  custom:scripting/monoscript/fileName/WarpTextExample.cs: 2663459c266363641981c7d17b698d7f -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/StorageSerializer.cs: bd2e1f85a8c0c08db143374b1c4b7bc5 -> 
  custom:scripting/monoscript/fileName/ChatController.cs: 876d5ccae6e345dd5d9450eb0a748f16 -> 
  custom:scripting/monoscript/fileName/TextMeshSpawner.cs: 76f28e2ddb031571e6ec62b018f65fa8 -> 
  custom:scripting/monoscript/fileName/TeleType.cs: 31fa97821c4c70f157ec81d507aa6b62 -> 
  custom:scripting/monoscript/fileName/Benchmark04.cs: 32220b148620e52aa81c13a114862bf4 -> 
  custom:scripting/monoscript/fileName/Storage.cs: 0d55ff6f69c85da5c119baec6b4da9c9 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/TMP_PhoneNumberValidator.cs: 7c3049f481e464c6a9a15dee56c3126f -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/EnvMapAnimator.cs: 1b4f61c17e4b7283c3ac351aacff9d26 -> 
  custom:scripting/monoscript/fileName/ShaderPropAnimator.cs: 55ce425812446482e623ff2695c94cb2 -> 
  custom:scripting/monoscript/fileName/SimpleScript.cs: 454caab0c6c050f29b39d82e1d66d0fa -> 
  custom:scripting/monoscript/fileName/ObjectSpin.cs: 2b96bb2079dd7fa5108d981627a28e1e -> 
  custom:scripting/monoscript/fileName/CameraController.cs: 1daac0040b3fce2177f6ca4f90eb8c5b -> 90f26c7c63924735c23c826b8a781463
  custom:scripting/monoscript/fileName/TMP_TextInfoDebugTool.cs: 3f1709cfb2cc628169113a35b5e9be3f -> 
  custom:scripting/monoscript/fileName/StorageExample.cs: fbf56fbbe4a92ff023ad02de60c4da0e -> 
  custom:scripting/monoscript/fileName/StorageEncryption.cs: 1665512cf453609b5d2b62e0a4819a91 -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_A.cs: 347309a09d56e367b188a6978f75b424 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/StorageSettings.cs: 35ec0a7219481e610f2bc52f370f6422 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/VertexJitter.cs: d27eb5498e48714a66aece98da065133 -> 
  custom:scripting/monoscript/fileName/StorageTest.cs: 1c552e25ef1ff26b31190a379e271b83 -> 
  custom:scripting/monoscript/fileName/TextConsoleSimulator.cs: 33fc311abb20b5b0ed8f00e4d86b574e -> 
  custom:scripting/monoscript/fileName/VertexColorCycler.cs: 5f984ca91cf3609293aecc6bc5641572 -> 
  custom:scripting/monoscript/fileName/TMP_FrameRateCounter.cs: 46f655df8cb483ac5958307137b1585a -> 
  custom:scripting/monoscript/fileName/VertexShakeB.cs: a6f78c4fcda1a29a1a1c1baefedba9e7 -> 
  custom:scripting/monoscript/fileName/TMP_UiFrameRateCounter.cs: b01021aea19247f14a98ab338907956a -> 
  custom:scripting/monoscript/fileName/TMPro_InstructionOverlay.cs: b626d2270e6ebd5c4311585c18b58d8d -> 
  custom:scripting/monoscript/fileName/StorageCache.cs: 73093796b6294aaac7481c89ba959971 -> 
  custom:scripting/monoscript/fileName/TMP_DigitValidator.cs: ac02af96215ae3951a3cce9e1a8c6085 -> 
  custom:scripting/monoscript/fileName/UnityJsonConverters.cs: 801e98db15bb29b0fbdc94637601d63e -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/Benchmark01_UGUI.cs: 6148cfe2627b7708298f301788144139 -> 
  custom:scripting/monoscript/fileName/StorageMainThreadProcessor.cs: b90fbfcc1e7d025cb6b92e192e3f5bf7 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: d20f52e9a48edcbf1a1aa2c9240c389d -> 66c59ad6cee36014c710f4c18c6650dc
  custom:scripting/monoscript/fileName/Benchmark03.cs: 17cc87e0529ac71d909a946afb47a274 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.432 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.056 seconds
Domain Reload Profiling: 1488ms
	BeginReloadAssembly (121ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (241ms)
		LoadAssemblies (278ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (19ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1056ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (523ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (102ms)
			ProcessInitializeOnLoadAttributes (350ms)
			ProcessInitializeOnLoadMethodAttributes (61ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 8.20 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5958 Unused Serialized files (Serialized files now loaded: 0)
Unloading 30 unused Assets / (320.2 KB). Loaded Objects now: 6815.
Memory consumption went from 206.0 MB to 205.7 MB.
Total: 3.299500 ms (FindLiveObjects: 0.338600 ms CreateObjectMapping: 0.138800 ms MarkObjects: 2.706800 ms  DeleteObjects: 0.114700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SkewTextExample.cs: c142d02d70a129e0533975f7c2f96956 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/TMP_TextEventHandler.cs: f5cd9d5a43bfefc8f4fec80e6de896a4 -> 
  custom:scripting/monoscript/fileName/TextMeshProFloatingText.cs: 92608fa4a6315cd0786d82205e77b697 -> 
  custom:scripting/monoscript/fileName/VertexZoom.cs: 650ab1e81d40126e3edcf737cce646e4 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/VertexShakeA.cs: 63ec564c588058369499638dd899e05b -> 
  custom:scripting/monoscript/fileName/TMP_ExampleScript_01.cs: a9d4be556104ff54e7d240529b3e01dc -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/TMP_TextEventCheck.cs: 13505d917eab5868c435e0c2986dadfa -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_B.cs: ba2be21a7486d37b1011342977a2e49d -> 
  custom:scripting/monoscript/fileName/Benchmark01.cs: 2d3e2cd961730ab95f139a49a673bace -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/Benchmark02.cs: 5640963252ce70910e726415f869ced6 -> 
  custom:scripting/monoscript/fileName/DropdownSample.cs: 0a92251794e5f10400149f96c3c59e5d -> 
  custom:scripting/monoscript/fileName/WarpTextExample.cs: 2663459c266363641981c7d17b698d7f -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/StorageSerializer.cs: bd2e1f85a8c0c08db143374b1c4b7bc5 -> 
  custom:scripting/monoscript/fileName/ChatController.cs: 876d5ccae6e345dd5d9450eb0a748f16 -> 
  custom:scripting/monoscript/fileName/TextMeshSpawner.cs: 76f28e2ddb031571e6ec62b018f65fa8 -> 
  custom:scripting/monoscript/fileName/TeleType.cs: 31fa97821c4c70f157ec81d507aa6b62 -> 
  custom:scripting/monoscript/fileName/Benchmark04.cs: 32220b148620e52aa81c13a114862bf4 -> 
  custom:scripting/monoscript/fileName/Storage.cs: 0d55ff6f69c85da5c119baec6b4da9c9 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/TMP_PhoneNumberValidator.cs: 7c3049f481e464c6a9a15dee56c3126f -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/EnvMapAnimator.cs: 1b4f61c17e4b7283c3ac351aacff9d26 -> 
  custom:scripting/monoscript/fileName/ShaderPropAnimator.cs: 55ce425812446482e623ff2695c94cb2 -> 
  custom:scripting/monoscript/fileName/SimpleScript.cs: 454caab0c6c050f29b39d82e1d66d0fa -> 
  custom:scripting/monoscript/fileName/ObjectSpin.cs: 2b96bb2079dd7fa5108d981627a28e1e -> 
  custom:scripting/monoscript/fileName/CameraController.cs: 1daac0040b3fce2177f6ca4f90eb8c5b -> 90f26c7c63924735c23c826b8a781463
  custom:scripting/monoscript/fileName/TMP_TextInfoDebugTool.cs: 3f1709cfb2cc628169113a35b5e9be3f -> 
  custom:scripting/monoscript/fileName/StorageExample.cs: fbf56fbbe4a92ff023ad02de60c4da0e -> 
  custom:scripting/monoscript/fileName/StorageEncryption.cs: 1665512cf453609b5d2b62e0a4819a91 -> 
  custom:scripting/monoscript/fileName/TMP_TextSelector_A.cs: 347309a09d56e367b188a6978f75b424 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/StorageSettings.cs: 35ec0a7219481e610f2bc52f370f6422 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/VertexJitter.cs: d27eb5498e48714a66aece98da065133 -> 
  custom:scripting/monoscript/fileName/StorageTest.cs: 1c552e25ef1ff26b31190a379e271b83 -> 
  custom:scripting/monoscript/fileName/TextConsoleSimulator.cs: 33fc311abb20b5b0ed8f00e4d86b574e -> 
  custom:scripting/monoscript/fileName/VertexColorCycler.cs: 5f984ca91cf3609293aecc6bc5641572 -> 
  custom:scripting/monoscript/fileName/TMP_FrameRateCounter.cs: 46f655df8cb483ac5958307137b1585a -> 
  custom:scripting/monoscript/fileName/VertexShakeB.cs: a6f78c4fcda1a29a1a1c1baefedba9e7 -> 
  custom:scripting/monoscript/fileName/TMP_UiFrameRateCounter.cs: b01021aea19247f14a98ab338907956a -> 
  custom:scripting/monoscript/fileName/TMPro_InstructionOverlay.cs: b626d2270e6ebd5c4311585c18b58d8d -> 
  custom:scripting/monoscript/fileName/StorageCache.cs: 73093796b6294aaac7481c89ba959971 -> 
  custom:scripting/monoscript/fileName/TMP_DigitValidator.cs: ac02af96215ae3951a3cce9e1a8c6085 -> 
  custom:scripting/monoscript/fileName/UnityJsonConverters.cs: 801e98db15bb29b0fbdc94637601d63e -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/Benchmark01_UGUI.cs: 6148cfe2627b7708298f301788144139 -> 
  custom:scripting/monoscript/fileName/StorageMainThreadProcessor.cs: b90fbfcc1e7d025cb6b92e192e3f5bf7 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: d20f52e9a48edcbf1a1aa2c9240c389d -> 66c59ad6cee36014c710f4c18c6650dc
  custom:scripting/monoscript/fileName/Benchmark03.cs: 17cc87e0529ac71d909a946afb47a274 -> 
