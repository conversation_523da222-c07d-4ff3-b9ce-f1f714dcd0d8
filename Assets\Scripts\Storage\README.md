# Storage - 高性能数据持久化模块

Storage是一个专为Unity设计的高性能数据持久化模块，用于替代Easy Save 3插件。它提供了基于key-value的数据增删改查操作，支持内存缓存和文件持久化，具有优秀的性能表现和丰富的功能特性。

## ⚠️ 重要变更说明

**版本更新：从async/await改为Thread + 回调模式**

为了更好地控制线程行为和避免async/await的潜在问题，Storage模块已从异步模式改为使用Thread + 回调的模式：

- ✅ **新方式**：`Storage.SaveToFileAsync(settings, callback)`
- ❌ **旧方式**：`await Storage.SaveToFileAsync(settings)`

所有文件操作现在都在后台线程执行，通过回调通知主线程操作结果。这样可以：
- 更好地控制线程生命周期
- 避免async/await的内存开销
- 提供更清晰的错误处理机制
- 确保回调在Unity主线程执行

## 主要特性

### 🚀 核心功能
- **内存+文件双重存储机制**：数据默认存储在内存中，提供快速访问，仅在显式调用时才持久化到文件
- **基于key-value的操作**：简洁易用的API，支持增删改查操作
- **选择性保存**：可指定保存特定key或全部数据
- **JSON格式存储**：使用JSON格式进行文件存储，确保可读性和跨平台兼容性
- **后台线程文件操作**：使用Thread在后台执行文件I/O操作，通过回调通知主线程，避免主线程阻塞
- **安全文件保存**：采用.bak备份机制和原子操作，确保数据完整性和可恢复性

### 🔒 安全特性
- **AES加密**：支持AES-256加密算法，确保数据安全
- **XOR加密**：支持轻量级XOR异或加密
- **密码保护**：可自定义加密密码

### 📊 数据类型支持
- **基础类型**：int, float, string, byte, bool, char, double, long, short, uint, ulong, ushort, Enum, DateTime, Guid
- **集合类型**：Array[], Dictionary<TKey,TValue>, List<T>, Queue<T>, HashSet<T>, Stack<T>
- **Unity类型**：Vector2, Vector3, Vector4, Quaternion, Rect, Bounds, Color, Color32, Matrix4x4
- **自定义类型**：支持Struct和Class对象的序列化

### ⚡ 性能优化
- **线程安全**：使用ConcurrentDictionary确保多线程环境下的安全性
- **内存优化**：避免不必要的内存分配，减少GC压力
- **高效序列化**：基于Newtonsoft.Json的高性能序列化
- **缓存机制**：智能的脏标记系统，只在数据变更时才进行保存
- **后台线程操作**：使用Thread在后台执行文件I/O，通过回调机制通知主线程，避免主线程阻塞
- **安全保存**：原子文件操作和备份恢复机制，确保数据完整性

## 快速开始

### 1. 基本使用

```csharp
using Storage;

// 初始化Storage系统
Storage.Initialize();

// 保存数据到内存
Storage.Set("PlayerLevel", 10);
Storage.Set("PlayerName", "Hero");
Storage.Set("PlayerPosition", new Vector3(1, 2, 3));

// 从内存获取数据 - 方法1：使用TryGet
if (Storage.TryGet<int>("PlayerLevel", out int level))
{
    // 键存在且类型正确，level已被赋值
    Debug.Log($"玩家等级: {level}");
}
else
{
    // 键不存在或类型不正确
    level = 1; // 使用默认值
    Debug.Log("使用默认玩家等级: 1");
}

// 从内存获取数据 - 方法2：使用带默认值的TryGet
if (Storage.TryGet("PlayerName", out string name, "DefaultHero"))
{
    // 键存在，name已被赋值为存储的值
    Debug.Log($"玩家名称: {name}");
}
else
{
    // 键不存在，name已被赋值为默认值"DefaultHero"
    Debug.Log($"使用默认玩家名称: {name}");
}

// 获取Vector3类型数据，使用默认值
Storage.TryGet("PlayerPosition", out Vector3 position, Vector3.zero);
Debug.Log($"玩家位置: {position}");

// 保存到文件（带回调）
Storage.SaveToFileAsync(null, (success, errorMessage) =>
{
    if (success)
    {
        Debug.Log("数据保存成功！");
    }
    else
    {
        Debug.LogError($"数据保存失败: {errorMessage}");
    }
});

// 从文件加载（带回调）
Storage.LoadFromFile(null, (success, data, errorMessage) =>
{
    if (success)
    {
        Debug.Log($"数据加载成功！加载了 {data?.Count ?? 0} 项数据");
    }
    else
    {
        Debug.LogError($"数据加载失败: {errorMessage}");
    }
});
```

### 2. 自定义设置

```csharp
// 创建自定义设置
var settings = new StorageSettings("MyGameData.json");
settings.Encryption = StorageSettings.EncryptionType.AES;
settings.EncryptionPassword = "MySecretPassword";
settings.PrettyPrint = true;

// 使用自定义设置初始化
Storage.Initialize(settings);

// 使用自定义设置保存
Storage.SaveToFileAsync(settings, (success, errorMessage) =>
{
    if (success)
    {
        Debug.Log("使用自定义设置保存成功！");
    }
    else
    {
        Debug.LogError($"保存失败: {errorMessage}");
    }
});
```

### 3. 加密功能

```csharp
// AES加密
var aesSettings = new StorageSettings("EncryptedData.json",
    StorageSettings.EncryptionType.AES, "MyPassword");

// XOR加密
var xorSettings = new StorageSettings("XORData.json",
    StorageSettings.EncryptionType.XOR, "MyPassword");

Storage.SaveToFileAsync(aesSettings, (success, errorMessage) =>
{
    if (success)
    {
        Debug.Log("加密数据保存成功！");
    }
    else
    {
        Debug.LogError($"加密数据保存失败: {errorMessage}");
    }
});
```

### 4. 后台线程文件操作

```csharp
// 后台线程保存（带回调）
Storage.SaveToFileAsync(null, (success, errorMessage) =>
{
    if (success)
    {
        Debug.Log("后台保存完成！");
        // 可以在这里执行保存完成后的逻辑
    }
    else
    {
        Debug.LogError($"后台保存失败: {errorMessage}");
    }
});

// 后台线程加载（带回调）
Storage.LoadFromFile(null, (success, data, errorMessage) =>
{
    if (success)
    {
        Debug.Log($"后台加载完成！加载了 {data?.Count ?? 0} 项数据");
        // 可以在这里执行加载完成后的逻辑
    }
    else
    {
        Debug.LogError($"后台加载失败: {errorMessage}");
    }
});
```

### 5. 安全文件保存

Storage模块自动使用安全的文件保存机制：
- 先写入.bak备份文件
- 原子操作替换主文件
- 启动时自动检测并恢复损坏的文件

## API参考

### Storage 静态类

#### 初始化方法
- `Initialize(StorageSettings settings = null)` - 初始化存储系统

#### 数据操作方法
- `Set<T>(string key, T value)` - 保存数据到内存缓存
- `Get<T>(string key)` - 从内存缓存获取数据
- `Get<T>(string key, T defaultValue)` - 获取数据，不存在时返回默认值
- `ContainsKey(string key)` - 检查键是否存在
- `Remove(string key)` - 删除数据
- `Clear()` - 清空所有数据
- `GetAllKeys()` - 获取所有键

#### 文件操作方法
- `SaveToFileAsync(StorageSettings settings = null, FileOperationCallback callback = null)` - 在后台线程保存所有数据到文件
- `LoadFromFileAsync(StorageSettings settings = null, FileReadCallback callback = null)` - 在后台线程从文件加载数据
- `SaveToFileSync(StorageSettings settings = null)` - 同步保存所有数据到文件
- `LoadFromFileSync(StorageSettings settings = null)` - 同步从文件加载数据
- `ExecuteMainThreadActions()` - 处理主线程队列中的回调操作（由StorageMainThreadProcessor自动调用）
- `FileExists(StorageSettings settings = null)` - 检查文件是否存在
- `DeleteFile(StorageSettings settings = null)` - 删除文件

### 回调委托类型

#### FileOperationCallback
```csharp
public delegate void FileOperationCallback(bool success, string errorMessage = null);
```
用于文件保存操作的回调委托：
- `success`: 操作是否成功
- `errorMessage`: 错误信息（如果有）

#### FileReadCallback
```csharp
public delegate void FileReadCallback(bool success, Dictionary<string, object> data = null, string errorMessage = null);
```
用于文件读取操作的回调委托：
- `success`: 操作是否成功
- `data`: 读取的数据（如果成功）
- `errorMessage`: 错误信息（如果有）

### StorageSettings 配置类

#### 主要属性
- `FilePath` - 文件路径
- `Location` - 存储位置（File, PlayerPrefs, Memory）
- `Directory` - 文件目录类型
- `Encryption` - 加密类型
- `EncryptionPassword` - 加密密码
- `PrettyPrint` - 是否格式化JSON输出
- `AutoSaveOnApplicationPause` - 应用暂停时自动保存
- `AutoSaveOnApplicationQuit` - 应用退出时自动保存

## 支持的数据类型

### 基础类型
```csharp
Storage.Set("IntValue", 42);
Storage.Set("FloatValue", 3.14f);
Storage.Set("StringValue", "Hello World");
Storage.Set("BoolValue", true);
Storage.Set("DateTimeValue", DateTime.Now);
Storage.Set("GuidValue", Guid.NewGuid());
```

### Unity类型
```csharp
Storage.Set("Position", new Vector3(1, 2, 3));
Storage.Set("Rotation", Quaternion.identity);
Storage.Set("Color", Color.red);
Storage.Set("Bounds", new Bounds(Vector3.zero, Vector3.one));
```

### 集合类型
```csharp
Storage.Set("IntArray", new int[] { 1, 2, 3, 4, 5 });
Storage.Set("StringList", new List<string> { "A", "B", "C" });
Storage.Set("Dictionary", new Dictionary<string, int> { {"Key1", 1}, {"Key2", 2} });
Storage.Set("HashSet", new HashSet<string> { "Item1", "Item2" });
```

### 自定义类型
```csharp
[Serializable]
public class PlayerData
{
    public string Name;
    public int Level;
    public Vector3 Position;
}

var playerData = new PlayerData { Name = "Hero", Level = 10, Position = Vector3.zero };
Storage.Set("PlayerData", playerData);
```

## 性能特性

### 内存优化
- 使用ConcurrentDictionary确保线程安全
- 智能的脏标记系统，避免不必要的文件写入
- 对象池模式处理频繁创建/销毁的对象

### 序列化优化
- 基于Newtonsoft.Json的高性能序列化
- 自定义Unity类型转换器，优化序列化性能
- 支持类型检查，确保序列化安全性

### 加密优化
- AES-256加密算法，平衡安全性和性能
- XOR加密提供轻量级加密选项
- 密钥派生算法确保密码安全性

## 最佳实践

### 1. 初始化
建议在游戏启动时初始化Storage系统：
```csharp
void Awake()
{
    var settings = new StorageSettings("GameData.json");
    settings.AutoSaveOnApplicationQuit = true;
    Storage.Initialize(settings);
}
```

### 2. 数据组织
使用有意义的键名组织数据：
```csharp
// 推荐
Storage.Set("Player.Level", level);
Storage.Set("Player.Experience", experience);
Storage.Set("Settings.SoundVolume", volume);

// 不推荐
Storage.Set("data1", level);
Storage.Set("data2", experience);
```

### 3. 错误处理
始终使用带默认值的TryGet方法处理可能不存在的数据：
```csharp
Storage.TryGet("Player.Level", out int level, 1); // 默认等级为1
Storage.TryGet("Settings.Sound", out bool soundEnabled, true); // 默认开启声音
```

### 4. 性能考虑
- 频繁变化的数据可以批量保存
- 使用选择性保存减少文件写入次数
- 在合适的时机调用SaveToFileAsync()
- 对于大量数据或避免卡顿，优先使用带回调的后台线程方法
- 利用安全保存机制，无需担心数据损坏问题

## 示例项目

项目中包含了完整的示例脚本 `StorageExample.cs`，演示了：
- 基本的数据保存和加载
- 不同数据类型的使用
- 加密功能的配置
- 自定义类型的序列化
- 用户界面交互

## 与Easy Save 3的对比

| 特性 | Storage | Easy Save 3 |
|------|---------|-------------|
| 性能 | 高性能，优化的内存使用 | 中等性能 |
| API设计 | 简洁直观 | 功能丰富但复杂 |
| 加密支持 | AES + XOR | AES |
| 文件格式 | JSON | JSON/Binary/XML |
| 内存缓存 | 内置高效缓存 | 可选缓存 |
| 线程安全 | 完全线程安全 | 部分线程安全 |
| 代码大小 | 轻量级 | 较大 |

## 注意事项

1. **Newtonsoft.Json依赖**：确保项目中已安装Newtonsoft.Json包
2. **序列化限制**：只有public字段或标记[SerializeField]的字段会被序列化
3. **平台兼容性**：WebGL平台会自动使用PlayerPrefs存储
4. **加密性能**：AES加密会增加一定的性能开销，根据需要选择合适的加密方式

## 许可证

本项目采用MIT许可证，可自由使用和修改。
