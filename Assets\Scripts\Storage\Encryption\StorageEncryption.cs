using System;
using System.IO;
using System.Text;
using System.Security.Cryptography;
using UnityEngine;
using DGame.Framework;

namespace Storage.Encryption
{
    /// <summary>
    /// 存储系统的加密管理器
    /// </summary>
    public static class StorageEncryption
    {
        #region AES加密

        /// <summary>
        /// 使用AES加密字符串
        /// </summary>
        /// <param name="plainText">明文</param>
        /// <param name="password">密码</param>
        /// <returns>加密后的Base64字符串</returns>
        public static string EncryptAES(string plainText, string password)
        {
            if (string.IsNullOrEmpty(plainText))
            {
                NLogger.LogError("Plain text cannot be null or empty");
                return string.Empty;
            }

            if (string.IsNullOrEmpty(password))
            {
                NLogger.LogError("Password cannot be null or empty");
                return plainText;
            }

            try
            {
                byte[] plainBytes = Encoding.UTF8.GetBytes(plainText);
                byte[] encryptedBytes = EncryptAES(plainBytes, password);
                return Convert.ToBase64String(encryptedBytes);
            }
            catch (Exception ex)
            {
                NLogger.LogError("Encryption failed: {0}", arg0: ex.Message);
                return plainText;
            }
        }

        /// <summary>
        /// 使用AES解密字符串
        /// </summary>
        /// <param name="encryptedText">加密的Base64字符串</param>
        /// <param name="password">密码</param>
        /// <returns>解密后的明文</returns>
        public static string DecryptAES(string encryptedText, string password)
        {
            if (string.IsNullOrEmpty(encryptedText))
            {
                NLogger.LogError("Encrypted text cannot be null or empty");
                return string.Empty;
            }

            if (string.IsNullOrEmpty(password))
            {
                NLogger.LogError("Password cannot be null or empty");
                return encryptedText;
            }

            try
            {
                byte[] encryptedBytes = Convert.FromBase64String(encryptedText);
                byte[] decryptedBytes = DecryptAES(encryptedBytes, password);
                return Encoding.UTF8.GetString(decryptedBytes);
            }
            catch (Exception ex)
            {
                NLogger.LogError("Decryption failed: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                return encryptedText;
            }
        }

        /// <summary>
        /// 使用AES加密字节数组
        /// </summary>
        /// <param name="plainBytes">明文字节数组</param>
        /// <param name="password">密码</param>
        /// <returns>加密后的字节数组</returns>
        public static byte[] EncryptAES(byte[] plainBytes, string password)
        {
            if (plainBytes == null || plainBytes.Length == 0)
            {
                NLogger.LogError("Plain bytes cannot be null or empty");
                return new byte[0];
            }

            try
            {
                using (Aes aes = Aes.Create())
                {
                    // 生成密钥和IV
                    var key = GenerateKey(password, aes.KeySize / 8);
                    aes.Key = key;
                    aes.GenerateIV();

                    using (var encryptor = aes.CreateEncryptor())
                    using (var msEncrypt = new MemoryStream())
                    {
                        // 先写入IV
                        msEncrypt.Write(aes.IV, 0, aes.IV.Length);

                        using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                        {
                            csEncrypt.Write(plainBytes, 0, plainBytes.Length);
                        }

                        return msEncrypt.ToArray();
                    }
                }
            }
            catch (Exception ex)
            {
                NLogger.LogError("Encryption failed: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                return plainBytes;
            }
        }

        /// <summary>
        /// 使用AES解密字节数组
        /// </summary>
        /// <param name="encryptedBytes">加密的字节数组</param>
        /// <param name="password">密码</param>
        /// <returns>解密后的字节数组</returns>
        public static byte[] DecryptAES(byte[] encryptedBytes, string password)
        {
            if (encryptedBytes == null || encryptedBytes.Length == 0)
            {
                NLogger.LogError("Encrypted bytes cannot be null or empty");
                return new byte[0];
            }

            try
            {
                using (Aes aes = Aes.Create())
                {
                    // 生成密钥
                    var key = GenerateKey(password, aes.KeySize / 8);
                    aes.Key = key;

                    // 提取IV
                    var iv = new byte[aes.BlockSize / 8];
                    Array.Copy(encryptedBytes, 0, iv, 0, iv.Length);
                    aes.IV = iv;

                    using (var decryptor = aes.CreateDecryptor())
                    using (var msDecrypt = new MemoryStream(encryptedBytes, iv.Length, encryptedBytes.Length - iv.Length))
                    using (var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    using (var msPlain = new MemoryStream())
                    {
                        csDecrypt.CopyTo(msPlain);
                        return msPlain.ToArray();
                    }
                }
            }
            catch (Exception ex)
            {
                NLogger.LogError("Decryption failed: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                return encryptedBytes;
            }
        }

        #endregion

        #region XOR加密

        /// <summary>
        /// 使用XOR加密字符串
        /// </summary>
        /// <param name="text">要加密的文本</param>
        /// <param name="password">密码</param>
        /// <returns>加密后的Base64字符串</returns>
        public static string EncryptXOR(string text, string password)
        {
            if (string.IsNullOrEmpty(text))
            {
                NLogger.LogError("Text cannot be null or empty");
                return string.Empty;
            }

            if (string.IsNullOrEmpty(password))
            {
                NLogger.LogError("Password cannot be null or empty");
                return text;
            }

            try
            {
                byte[] textBytes = Encoding.UTF8.GetBytes(text);
                byte[] encryptedBytes = EncryptXOR(textBytes, password);
                return Convert.ToBase64String(encryptedBytes);
            }
            catch (Exception ex)
            {
                NLogger.LogError("Encryption failed: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                return text;
            }
        }

        /// <summary>
        /// 使用XOR解密字符串
        /// </summary>
        /// <param name="encryptedText">加密的Base64字符串</param>
        /// <param name="password">密码</param>
        /// <returns>解密后的文本</returns>
        public static string DecryptXOR(string encryptedText, string password)
        {
            if (string.IsNullOrEmpty(encryptedText))
            {
                NLogger.LogError("Encrypted text cannot be null or empty");
                return string.Empty;
            }

            if (string.IsNullOrEmpty(password))
            {
                NLogger.LogError("Password cannot be null or empty");
                return encryptedText;
            }

            try
            {
                byte[] encryptedBytes = Convert.FromBase64String(encryptedText);
                byte[] decryptedBytes = DecryptXOR(encryptedBytes, password);
                return Encoding.UTF8.GetString(decryptedBytes);
            }
            catch (Exception ex)
            {
                NLogger.LogError("Decryption failed: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                return encryptedText;
            }
        }

        /// <summary>
        /// 使用XOR加密字节数组
        /// </summary>
        /// <param name="data">要加密的数据</param>
        /// <param name="password">密码</param>
        /// <returns>加密后的字节数组</returns>
        public static byte[] EncryptXOR(byte[] data, string password)
        {
            if (data == null || data.Length == 0)
            {
                NLogger.LogError("Data cannot be null or empty");
                return new byte[0];
            }

            byte[] passwordBytes = Encoding.UTF8.GetBytes(password);
            byte[] result = new byte[data.Length];

            for (int i = 0; i < data.Length; ++i)
            {
                result[i] = (byte)(data[i] ^ passwordBytes[i % passwordBytes.Length]);
            }

            return result;
        }

        /// <summary>
        /// 使用XOR解密字节数组（XOR加密和解密是相同的操作）
        /// </summary>
        /// <param name="encryptedData">加密的数据</param>
        /// <param name="password">密码</param>
        /// <returns>解密后的字节数组</returns>
        public static byte[] DecryptXOR(byte[] encryptedData, string password)
        {
            // XOR加密和解密是相同的操作
            return EncryptXOR(encryptedData, password);
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 从密码生成固定长度的密钥
        /// </summary>
        /// <param name="password">密码</param>
        /// <param name="keyLength">密钥长度</param>
        /// <returns>生成的密钥</returns>
        private static byte[] GenerateKey(string password, int keyLength)
        {
            using (var sha256 = SHA256.Create())
            {
                byte[] passwordBytes = Encoding.UTF8.GetBytes(password);
                byte[] hash = sha256.ComputeHash(passwordBytes);

                // 如果哈希长度不够，重复哈希
                if (hash.Length < keyLength)
                {
                    byte[] extendedKey = new byte[keyLength];
                    for (int i = 0; i < keyLength; ++i)
                    {
                        extendedKey[i] = hash[i % hash.Length];
                    }
                    return extendedKey;
                }

                // 如果哈希长度过长，截取
                if (hash.Length > keyLength)
                {
                    byte[] truncatedKey = new byte[keyLength];
                    Array.Copy(hash, truncatedKey, keyLength);
                    return truncatedKey;
                }

                return hash;
            }
        }

        #endregion
    }
}
