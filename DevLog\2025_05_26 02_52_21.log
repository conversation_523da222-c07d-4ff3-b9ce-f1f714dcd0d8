2025-05-26 02:52:22.0225 [INFO] [StorageSettings]::InitializePathCache(87) - Storage paths cached successfully
2025-05-26 02:52:22.0423 [INFO] [Storage]::Initialize(128) - Storage system initialized successfully
2025-05-26 02:52:22.0423 [INFO] [StorageCache]::Set(87) - Key: test_string, Type: String
2025-05-26 02:52:22.0423 [INFO] [Storage]::Set(161) - Key: test_string, Type: String
2025-05-26 02:52:22.0423 [INFO] [StorageCache]::Set(87) - Key: test_int, Type: Int32
2025-05-26 02:52:22.0423 [INFO] [Storage]::Set(161) - Key: test_int, Type: Int32
2025-05-26 02:52:22.0423 [INFO] [StorageCache]::Set(87) - Key: test_float, Type: Single
2025-05-26 02:52:22.0423 [INFO] [Storage]::Set(161) - Key: test_float, Type: Single
2025-05-26 02:52:22.0423 [INFO] [StorageCache]::Set(87) - Key: test_bool, Type: Boolean
2025-05-26 02:52:22.0423 [INFO] [Storage]::Set(161) - Key: test_bool, Type: Boolean
2025-05-26 02:52:22.0820 [INFO] [Storage]::WriteToFileWithBackup(671) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\SaveData.json
2025-05-26 02:52:22.5290 [INFO] [Storage]::SaveToFile(294) - Saved 4 items to file: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\SaveData.json
2025-05-26 02:52:22.5290 [INFO] [StorageCache]::Clear(199) - Cleared 4 items from cache
2025-05-26 02:52:22.5290 [INFO] [Storage]::Clear(243) - All data cleared from cache
2025-05-26 02:52:22.5868 [INFO] [StorageCache]::LoadFromDictionary(234) - Loaded 4 items into cache
2025-05-26 02:52:22.5868 [INFO] [Storage]::LoadFromFile(439) - Loaded 4 items from file: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\SaveData.json
2025-05-26 02:52:22.5868 [ERROR] [StorageCache]::Get(112) - Cannot cast value for key 'test_int' to type Int32
StackTrace:   at Storage.StorageCache.Get[T] (System.String key) [0x00048] in H:\DiceGame\DGame\Assets\Scripts\Storage\Core\StorageCache.cs:108 
2025-05-26 02:52:22.5868 [ERROR] [StorageCache]::Get(112) - Cannot cast value for key 'test_float' to type Single
StackTrace:   at Storage.StorageCache.Get[T] (System.String key) [0x00048] in H:\DiceGame\DGame\Assets\Scripts\Storage\Core\StorageCache.cs:108 
