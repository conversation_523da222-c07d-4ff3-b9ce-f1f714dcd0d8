using System;
using System.Collections.Generic;
using System.Text;
using UnityEngine;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using DGame.Framework;

namespace Storage.Serialization
{
    /// <summary>
    /// 存储系统的JSON序列化器，负责数据的序列化和反序列化
    /// </summary>
    public static class StorageSerializer
    {
        #region 私有字段

        private static JsonSerializerSettings _serializerSettings;

        #endregion

        #region 属性

        /// <summary>
        /// JSON序列化设置
        /// </summary>
        private static JsonSerializerSettings SerializerSettings
        {
            get
            {
                if (_serializerSettings == null)
                {
                    _serializerSettings = new JsonSerializerSettings
                    {
                        TypeNameHandling = TypeNameHandling.Auto,
                        NullValueHandling = NullValueHandling.Include,
                        DefaultValueHandling = DefaultValueHandling.Include,
                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                        Converters = new List<JsonConverter>
                        {
                            new Vector2Converter(),
                            new Vector3Converter(),
                            new Vector4Converter(),
                            new QuaternionConverter(),
                            new ColorConverter(),
                            new Color32Converter(),
                            new RectConverter(),
                            new BoundsConverter(),
                            new Matrix4x4Converter()
                        }
                    };
                }
                return _serializerSettings;
            }
        }

        #endregion

        #region 序列化方法

        /// <summary>
        /// 将数据字典序列化为JSON字符串
        /// </summary>
        /// <param name="data">数据字典</param>
        /// <param name="prettyPrint">是否格式化输出</param>
        /// <returns>JSON字符串</returns>
        public static string SerializeToJson(Dictionary<string, object> data, bool prettyPrint = true)
        {
            if (data == null)
            {
                NLogger.LogError("Data dictionary cannot be null");
                return "{}";
            }

            try
            {
                var formatting = prettyPrint ? Formatting.Indented : Formatting.None;
                return JsonConvert.SerializeObject(data, formatting, SerializerSettings);
            }
            catch (Exception ex)
            {
                NLogger.LogError("Serialization failed: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                return "{}";
            }
        }

        /// <summary>
        /// 将单个对象序列化为JSON字符串
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要序列化的对象</param>
        /// <param name="prettyPrint">是否格式化输出</param>
        /// <returns>JSON字符串</returns>
        public static string SerializeObject<T>(T obj, bool prettyPrint = true)
        {
            try
            {
                var formatting = prettyPrint ? Formatting.Indented : Formatting.None;
                return JsonConvert.SerializeObject(obj, formatting, SerializerSettings);
            }
            catch (Exception ex)
            {
                NLogger.LogError("Serialization failed: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                return "null";
            }
        }

        #endregion

        #region 反序列化方法

        /// <summary>
        /// 从JSON字符串反序列化为数据字典
        /// </summary>
        /// <param name="json">JSON字符串</param>
        /// <returns>数据字典</returns>
        public static Dictionary<string, object> DeserializeFromJson(string json)
        {
            if (string.IsNullOrEmpty(json))
            {
                NLogger.LogWarning("JSON string is null or empty");
                return new Dictionary<string, object>();
            }

            try
            {
                var result = JsonConvert.DeserializeObject<Dictionary<string, object>>(json, SerializerSettings);
                return result ?? new Dictionary<string, object>();
            }
            catch (Exception ex)
            {
                NLogger.LogError("Deserialization failed: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                return new Dictionary<string, object>();
            }
        }

        /// <summary>
        /// 从JSON字符串反序列化为指定类型的对象
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="json">JSON字符串</param>
        /// <returns>反序列化的对象</returns>
        public static T DeserializeObject<T>(string json)
        {
            if (string.IsNullOrEmpty(json))
            {
                NLogger.LogWarning("JSON string is null or empty");
                return default;
            }

            try
            {
                return JsonConvert.DeserializeObject<T>(json, SerializerSettings);
            }
            catch (Exception ex)
            {
                NLogger.LogError("Deserialization failed: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                return default;
            }
        }

        /// <summary>
        /// 从JSON字符串反序列化为指定类型的对象，失败时返回默认值
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="json">JSON字符串</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>反序列化的对象或默认值</returns>
        public static T DeserializeObject<T>(string json, T defaultValue)
        {
            if (string.IsNullOrEmpty(json))
            {
                return defaultValue;
            }

            try
            {
                var result = JsonConvert.DeserializeObject<T>(json, SerializerSettings);
                return result != null ? result : defaultValue;
            }
            catch (Exception ex)
            {
                NLogger.LogError("Deserialization failed: {0}", arg0: ex.Message);
                return defaultValue;
            }
        }

        #endregion

        #region 类型检查方法

        /// <summary>
        /// 检查类型是否可序列化
        /// </summary>
        /// <param name="type">要检查的类型</param>
        /// <returns>是否可序列化</returns>
        public static bool IsSerializable(Type type)
        {
            if (type == null) return false;

            // 基础类型
            if (type.IsPrimitive || type == typeof(string) || type == typeof(decimal) ||
                type == typeof(DateTime) || type == typeof(Guid)) return true;

            // 枚举类型
            if (type.IsEnum) return true;

            // Unity类型
            if (IsUnityType(type)) return true;

            // 集合类型
            if (IsCollectionType(type)) return true;

            // 可序列化的类和结构体
            if (type.IsClass || type.IsValueType)
            {
                return type.IsSerializable || HasSerializableFields(type);
            }

            return false;
        }

        /// <summary>
        /// 检查是否为Unity类型
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>是否为Unity类型</returns>
        private static bool IsUnityType(Type type)
        {
            return type == typeof(Vector2) || type == typeof(Vector3) || type == typeof(Vector4) ||
                type == typeof(Quaternion) || type == typeof(Color) || type == typeof(Color32) ||
                type == typeof(Rect) || type == typeof(Bounds) || type == typeof(Matrix4x4);
        }

        /// <summary>
        /// 检查是否为集合类型
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>是否为集合类型</returns>
        private static bool IsCollectionType(Type type)
        {
            if (type.IsArray) return true;
            if (type.IsGenericType)
            {
                var genericType = type.GetGenericTypeDefinition();
                return genericType == typeof(List<>) || genericType == typeof(Dictionary<,>) ||
                    genericType == typeof(HashSet<>) || genericType == typeof(Queue<>) ||
                    genericType == typeof(Stack<>);
            }
            return false;
        }

        /// <summary>
        /// 检查类型是否有可序列化的字段
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>是否有可序列化字段</returns>
        private static bool HasSerializableFields(Type type)
        {
            var fields = type.GetFields(System.Reflection.BindingFlags.Public |
                                        System.Reflection.BindingFlags.NonPublic |
                                        System.Reflection.BindingFlags.Instance);

            foreach (var field in fields)
            {
                if (field.IsPublic || field.GetCustomAttributes(typeof(SerializeField), false).Length > 0)
                {
                    if (!field.IsLiteral && !field.IsInitOnly)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        #endregion
    }
}
