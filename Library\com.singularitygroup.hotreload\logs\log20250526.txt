2025-05-26 02:44:39.637 +08:00 [INF] Initialized user with id: 79F2-E01E-3AD4-5923-6A30-9ABA-7719-F292-19A4-6D89-EBD5-DCFA-826C-64FB-EF75-A89E
2025-05-26 02:44:39.749 +08:00 [INF] Hot Reload Server is starting. Do not close this window.
2025-05-26 02:44:39.749 +08:00 [INF] 
2025-05-26 02:44:40.752 +08:00 [INF] Compiling... (the first time might take a minute, depending on project size)
2025-05-26 02:44:42.839 +08:00 [INF] Finished compiling in 2s
2025-05-26 02:44:42.840 +08:00 [INF] 
2025-05-26 02:44:42.840 +08:00 [INF] Hot Reload Server is ready. Edit code to see updates in unity.
2025-05-26 02:44:42.840 +08:00 [INF] Keep this window open while using Hot Reload (minimizing is OK)
2025-05-26 02:50:03.518 +08:00 [INF] Detected change in H:/DiceGame/DGame/Assets\Scripts/Storage/Core/StorageSettings.cs
2025-05-26 02:50:05.141 +08:00 [INF] Scripts have compile errors: Assembly-CSharp StorageSettings.cs: H:\DiceGame\DGame\Assets\Scripts\Storage\Core\StorageSettings.cs(85,17): error CS0103: The name 'NLogger' does not exist in the current context
  H:\DiceGame\DGame\Assets\Scripts\Storage\Core\StorageSettings.cs(264,46): error CS0234: The type or namespace name 'Threading' does not exist in the namespace 'UnityEngine' (are you missing an assembly reference?)
  H:\DiceGame\DGame\Assets\Scripts\Storage\Core\StorageSettings.cs(264,108): error CS0234: The type or namespace name 'Threading' does not exist in the namespace 'UnityEngine' (are you missing an assembly reference?)
  H:\DiceGame\DGame\Assets\Scripts\Storage\Core\StorageSettings.cs(272,21): error CS0103: The name 'NLogger' does not exist in the current context
2025-05-26 02:50:15.579 +08:00 [INF] Detected change in H:/DiceGame/DGame/Assets\Scripts/Storage/Core/StorageSettings.cs
2025-05-26 02:50:15.902 +08:00 [INF] Scripts have compile errors: Assembly-CSharp StorageSettings.cs: H:\DiceGame\DGame\Assets\Scripts\Storage\Core\StorageSettings.cs(266,94): error CS0117: 'Storage' does not contain a definition for 'MainThreadId'
2025-05-26 02:50:35.547 +08:00 [INF] Detected change in H:/DiceGame/DGame/Assets\Scripts/Storage/Storage.cs
2025-05-26 02:50:36.366 +08:00 [INF] Exception occured when creating patches:
System.ArgumentException: opcode
   at Mono.Cecil.Cil.Instruction.Create(OpCode opcode, Instruction target)
   at ‌‍⁭‍⁯‪⁬⁮‮‬‍‫‏‮‭⁪‭‎⁯⁭⁮‬‬​‌‬⁮‭​⁭‏⁪‪‭‬​​​⁯‬‮.‏‮⁭⁪​‎‭‍⁭​‏⁫‎‌⁬‏⁬‫⁬⁯​‍⁬⁯⁬⁮⁪​‪⁪‫⁮⁫‎‮‎‌‏‪‮(OpCode, Instruction)
   at ‌‍⁭‍⁯‪⁬⁮‮‬‍‫‏‮‭⁪‭‎⁯⁭⁮‬‬​‌‬⁮‭​⁭‏⁪‪‭‬​​​⁯‬‮.‎​​⁪‏‮⁫‫‎‏‬‪⁯⁭⁫⁮⁪‪⁯‬‍⁭⁭‬‍‮‏⁮⁯‪​‍‪⁫‏‭‫‎‬⁫‮(ILProcessor , Instruction , FieldReference , GenericInstanceMethod , OpCode , ImmutableDictionary`2 )
   at ‌‍⁭‍⁯‪⁬⁮‮‬‍‫‏‮‭⁪‭‎⁯⁭⁮‬‬​‌‬⁮‭​⁭‏⁪‪‭‬​​​⁯‬‮.⁯⁯​‍‮​⁯‏⁯‍⁬‮⁪‬⁬⁪⁭‎​⁬⁫‬‭‬⁭‏⁯‍‫⁪‪⁪⁮‏⁪⁬​⁯‌‮(Instruction , ILProcessor , TypeReference , OpCode , ImmutableDictionary`2 )
   at ‌‍⁭‍⁯‪⁬⁮‮‬‍‫‏‮‭⁪‭‎⁯⁭⁮‬‬​‌‬⁮‭​⁭‏⁪‪‭‬​​​⁯‬‮.‎‮‮⁯‪‎‬‬⁭‬‪‏‫⁫​⁭‍‪⁬⁪‬‬‫​‫‏⁯⁬⁮‭‌‪‭‬⁮‍‬​⁭‭‮(FieldReference , MethodBody , Instruction )
   at ‏‏‏‫‭⁪⁪‬‎‫‌‌⁭⁯‬‫⁪‮⁮‍​‏‎‌‮‪⁯‫​​⁬​‍⁫‭⁯‫‬‏‍‮.‫‎‍⁮⁬‌⁯⁭‎‫‌⁭⁬‭‮‪‬‮‌‌‪⁭‌‬‏‏‍‍‮‎⁬⁮‌‬‭‭‎‍⁫‭‮(AssemblyDefinition , ‬⁪‎‬‫‏⁪‫⁮⁯‭‌‏⁪‭‎‬⁪‭‮‪⁪⁬‫‏⁫⁭‬‫⁬‫‪‏​‍‪​​⁬‭‮ )
   at ⁯​‮‬⁯⁬⁪‎‭⁪⁯‍‮‏⁮‍‎‮‮⁯​‭​⁮‮‌⁭⁯⁪⁯⁬‫‫⁬‍‎‭⁭⁭⁪‮.⁬⁫‏‪⁯‪‎‍‮⁯‭‮‭‪⁭‬‮⁬‌⁪‪⁭‎‎‍‭⁬‭⁭‭‎⁭‫‪⁬‭‮​‌⁭‮(String , String , Stream , Stream , Stream , Boolean , ImmutableArray`1 , ImmutableHashSet`1 , ⁯⁮⁯​​​⁬​⁮‭⁯‍‪⁮​​⁯‌‫⁭⁯‭‫‫‏⁮‫‏‌​​‫‎‏​‍‭⁫​‌‮ , ​⁫‬‏‮⁯⁯⁯‮‍​‬⁯⁪‬‎​⁯⁫‫‭‏‌⁮‫‪​​‌‮⁮⁫​‮⁪⁭‪‏⁪⁬‮ , ImmutableDictionary`2 , ImmutableDictionary`2 , ImmutableHashSet`1 , ImmutableDictionary`2 , ImmutableHashSet`1 , ImmutableHashSet`1 , ‫‫​​⁫‌‍‭‪‭⁬‪‭‎‪‪‫​​⁪‌⁭‎‌‎‫​⁬​‎‍⁯⁫​‍⁭‍⁬⁫⁮‮ , String , Boolean , ⁭‫‌⁯‏‌‬‭‏⁪⁫‏‬⁪⁮‮‌‌‭‭‏⁫⁪‮⁪‫⁮‍‌⁬‬‮⁮‮⁬‮⁯‮‎‭‮ , ⁮‬⁫‎⁬‬‬‭⁫‭‎⁫‮⁫⁫‏​‏⁬‌‏⁭‏‬‌‪​‍‫⁪‪‫‫⁪‮⁬⁯⁭‍‪‮ )
   at =`m}b)UO$IY\,T)A;>5N?=:Rs\,.‪‏⁫⁮‎‭‎⁯‏‪⁫⁪‪⁯⁯‌⁯⁮⁫​‬⁮‪⁮‫‏‍⁭‍‭‏⁯⁭⁯‭‏‭‫⁬⁮‮(ImmutableArray`1 , String , ImmutableDictionary`2 , Solution , ImmutableDictionary`2 , ImmutableDictionary`2 , ImmutableDictionary`2 , ImmutableDictionary`2 , ImmutableHashSet`1 , ImmutableDictionary`2 , ImmutableHashSet`1 , ⁯⁮⁯​​​⁬​⁮‭⁯‍‪⁮​​⁯‌‫⁭⁯‭‫‫‏⁮‫‏‌​​‫‎‏​‍‭⁫​‌‮ , ​⁫‬‏‮⁯⁯⁯‮‍​‬⁯⁪‬‎​⁯⁫‫‭‏‌⁮‫‪​​‌‮⁮⁫​‮⁪⁭‪‏⁪⁬‮ , ImmutableDictionary`2 , ImmutableDictionary`2 , ImmutableDictionary`2 , ImmutableDictionary`2 , Boolean , ⁭‫‌⁯‏‌‬‭‏⁪⁫‏‬⁪⁮‮‌‌‭‭‏⁫⁪‮⁪‫⁮‍‌⁬‬‮⁮‮⁬‮⁯‮‎‭‮ , ⁮‬⁫‎⁬‬‬‭⁫‭‎⁫‮⁫⁫‏​‏⁬‌‏⁭‏‬‌‪​‍‫⁪‪‫‫⁪‮⁬⁯⁭‍‪‮ )
   at =`m}b)UO$IY\,T)A;>5N?=:Rs\,.‪‏⁫⁮‎‭‎⁯‏‪⁫⁪‪⁯⁯‌⁯⁮⁫​‬⁮‪⁮‫‏‍⁭‍‭‏⁯⁭⁯‭‏‭‫⁬⁮‮(ImmutableArray`1 , String , ImmutableDictionary`2 , Solution , ImmutableDictionary`2 , ImmutableDictionary`2 , ImmutableDictionary`2 , ImmutableDictionary`2 , ImmutableHashSet`1 , ImmutableDictionary`2 , ImmutableHashSet`1 , ⁯⁮⁯​​​⁬​⁮‭⁯‍‪⁮​​⁯‌‫⁭⁯‭‫‫‏⁮‫‏‌​​‫‎‏​‍‭⁫​‌‮ , ​⁫‬‏‮⁯⁯⁯‮‍​‬⁯⁪‬‎​⁯⁫‫‭‏‌⁮‫‪​​‌‮⁮⁫​‮⁪⁭‪‏⁪⁬‮ , ImmutableDictionary`2 , ImmutableDictionary`2 , ImmutableDictionary`2 , ImmutableDictionary`2 , Boolean , ⁭‫‌⁯‏‌‬‭‏⁪⁫‏‬⁪⁮‮‌‌‭‭‏⁫⁪‮⁪‫⁮‍‌⁬‬‮⁮‮⁬‮⁯‮‎‭‮ , ⁮‬⁫‎⁬‬‬‭⁫‭‎⁫‮⁫⁫‏​‏⁬‌‏⁭‏‬‌‪​‍‫⁪‪‫‫⁪‮⁬⁯⁭‍‪‮ )
   at =`m}b)UO$IY\,T)A;>5N?=:Rs\,.‪‏⁫⁮‎‭‎⁯‏‪⁫⁪‪⁯⁯‌⁯⁮⁫​‬⁮‪⁮‫‏‍⁭‍‭‏⁯⁭⁯‭‏‭‫⁬⁮‮(ImmutableArray`1 , String , ImmutableDictionary`2 , Solution , ImmutableDictionary`2 , ImmutableDictionary`2 , ImmutableDictionary`2 , ImmutableDictionary`2 , ImmutableHashSet`1 , ImmutableDictionary`2 , ImmutableHashSet`1 , ⁯⁮⁯​​​⁬​⁮‭⁯‍‪⁮​​⁯‌‫⁭⁯‭‫‫‏⁮‫‏‌​​‫‎‏​‍‭⁫​‌‮ , ​⁫‬‏‮⁯⁯⁯‮‍​‬⁯⁪‬‎​⁯⁫‫‭‏‌⁮‫‪​​‌‮⁮⁫​‮⁪⁭‪‏⁪⁬‮ , ImmutableDictionary`2 , ImmutableDictionary`2 , ImmutableDictionary`2 , ImmutableDictionary`2 , Boolean , ⁭‫‌⁯‏‌‬‭‏⁪⁫‏‬⁪⁮‮‌‌‭‭‏⁫⁪‮⁪‫⁮‍‌⁬‬‮⁮‮⁬‮⁯‮‎‭‮ , ⁮‬⁫‎⁬‬‬‭⁫‭‎⁫‮⁫⁫‏​‏⁬‌‏⁭‏‬‌‪​‍‫⁪‪‫‫⁪‮⁬⁯⁭‍‪‮ )
   at =`m}b)UO$IY\,T)A;>5N?=:Rs\,.‍⁫‫​⁬‍⁪‎‭⁪‌‫⁫‮‌‍‫⁭⁬‍‏‪‎‭‎‏‫‭‎⁪‫⁬‭‫‬⁪​‪⁭⁮‮(Solution , Solution , ImmutableDictionary`2 , ImmutableDictionary`2 , ImmutableDictionary`2 , ImmutableDictionary`2 , 5k5yF'J?O$H)0<"M_H0WgEY7\+ , ImmutableDictionary`2 , ImmutableArray`1 , ImmutableArray`1 , String , ⁭‫‌⁯‏‌‬‭‏⁪⁫‏‬⁪⁮‮‌‌‭‭‏⁫⁪‮⁪‫⁮‍‌⁬‬‮⁮‮⁬‮⁯‮‎‭‮ , CancellationToken , Boolean , Boolean , ⁮‬⁫‎⁬‬‬‭⁫‭‎⁫‮⁫⁫‏​‏⁬‌‏⁭‏‬‌‪​‍‫⁪‪‫‫⁪‮⁬⁯⁭‍‪‮ )
   at =`m}b)UO$IY\,T)A;>5N?=:Rs\,.⁮‫‮​‪‍⁯‭‎⁮⁮‬‍‮‫‪‬‌‍‍⁮‎⁪‭‎‭⁬‪‌‫⁯‮‌​‍‍‫​‍‫‮(Solution , ImmutableDictionary`2 , ImmutableDictionary`2 , ImmutableDictionary`2 , ImmutableDictionary`2 , 5k5yF'J?O$H)0<"M_H0WgEY7\+ , ImmutableDictionary`2 , ImmutableArray`1 , ImmutableArray`1 , ImmutableDictionary`2 , String , ⁭‫‌⁯‏‌‬‭‏⁪⁫‏‬⁪⁮‮‌‌‭‭‏⁫⁪‮⁪‫⁮‍‌⁬‬‮⁮‮⁬‮⁯‮‎‭‮ , CancellationToken , Boolean , Boolean , ⁮‬⁫‎⁬‬‬‭⁫‭‎⁫‮⁫⁫‏​‏⁬‌‏⁭‏‬‌‪​‍‫⁪‪‫‫⁪‮⁬⁯⁭‍‪‮ )
   at =`m}b)UO$IY\,T)A;>5N?=:Rs\,.‌⁮​⁮‪⁭⁭⁫‫⁯‎‮‮⁬⁪‏​‪‍⁪⁮‫⁯​​‬⁮⁬⁪‭⁫​‪‮⁬‬⁭‭⁫‬‮(String , ‌‪‏⁬⁯‪⁬​‪‏⁫‌‮‪‍‭⁭‭‌‮‍⁯⁭‌​⁫⁭‫‎⁯⁪⁮⁪⁫⁪‎‎‎⁪⁪‮ , ImmutableArray`1 )
   at =`m}b)UO$IY\,T)A;>5N?=:Rs\,.‪‮⁬‎‪‫⁮‬‫⁭⁫⁬‌‌⁮‪‏‮‍‬⁮‬⁯⁮⁮⁭‌⁮⁫⁯⁬⁬‌‪⁯‎‬​⁯⁫‮(String , ‌‪‏⁬⁯‪⁬​‪‏⁫‌‮‪‍‭⁭‭‌‮‍⁯⁭‌​⁫⁭‫‎⁯⁪⁮⁪⁫⁪‎‎‎⁪⁪‮ , ImmutableArray`1 )
2025-05-26 02:50:47.732 +08:00 [INF] Detected change in H:/DiceGame/DGame/Assets\Scripts/Storage/Core/StorageSettings.cs
2025-05-26 02:50:48.040 +08:00 [INF] Scripts have compile errors: Assembly-CSharp StorageSettings.cs: H:\DiceGame\DGame\Assets\Scripts\Storage\Core\StorageSettings.cs(266,94): error CS0117: 'Storage' does not contain a definition for 'MainThreadId'
